#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VCF文件处理模块 - 梭菌生物信息学工作流程
作者：生物信息学分析团队
版本：3.0.0
描述：处理经过SnpEFF注释的VCF文件，提取非同义突变并进行质量控制
     基于真实的SnpEFF注释标准和VCF格式规范
"""

import logging
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Tuple, Union
from dataclasses import dataclass, field, asdict
from collections import defaultdict, Counter
import re
import gzip
import json

# 使用cyvcf2处理VCF文件（高性能的VCF处理库）
try:
    from cyvcf2 import VCF, Variant
    HAS_CYVCF2 = True
except ImportError:
    # 备用的PyVCF库
    try:
        import vcf as pyvcf
        HAS_CYVCF2 = False
        logger.warning("cyvcf2未安装，使用pyvcf作为备用（性能较低）")
    except ImportError:
        raise ImportError("请安装cyvcf2或pyvcf库：pip install cyvcf2 或 pip install pyvcf")

# 导入其他模块
from config_manager import ConfigManager

logger = logging.getLogger(__name__)


@dataclass
class MutationRecord:
    """突变记录数据结构 - 基于SnpEFF注释标准"""
    # 基本位置信息
    chromosome: str                        # 染色体/contig名称
    position: int                          # 基因组位置（1-based）
    reference: str                         # 参考等位基因
    alternate: str                         # 突变等位基因
    
    # 质量信息
    quality: float                         # 变异质量分数（PHRED）
    filter_status: str                     # 过滤状态（PASS, FAIL等）
    
    # SnpEFF注释信息（基于真实的SnpEFF输出格式）
    effect_type: str                       # 效应类型（missense_variant等）
    effect_impact: str                     # 影响等级（HIGH, MODERATE, LOW, MODIFIER）
    gene_id: str                          # 基因ID
    gene_name: str = ''                   # 基因名称
    transcript_id: str = ''               # 转录本ID
    
    # 蛋白质层面的变化
    protein_change: str = ''              # 蛋白质变化（如p.Ala123Val）
    codon_change: str = ''                # 密码子变化
    amino_acid_position: int = 0          # 氨基酸位置
    
    # 功能预测（基于真实预测工具的输出）
    sift_prediction: str = ''             # SIFT预测结果
    sift_score: float = 0.0               # SIFT分数
    polyphen_prediction: str = ''         # PolyPhen预测结果
    polyphen_score: float = 0.0           # PolyPhen分数
    cadd_score: float = 0.0               # CADD分数
    
    # 样本信息
    genotype: str = ''                    # 基因型（0/0, 0/1, 1/1等）
    allele_frequency: float = 0.0         # 等位基因频率
    read_depth: int = 0                   # 读取深度
    allele_depth: List[int] = field(default_factory=list)  # 各等位基因深度
    
    # 群体频率信息（如果有的话）
    population_frequency: Dict[str, float] = field(default_factory=dict)
    
    # 其他注释
    additional_annotations: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.additional_annotations:
            self.additional_annotations = {}
        if not self.allele_depth:
            self.allele_depth = []
        if not self.population_frequency:
            self.population_frequency = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    def get_mutation_key(self) -> str:
        """获取突变的唯一标识符"""
        return f"{self.chromosome}:{self.position}:{self.reference}>{self.alternate}"
    
    def is_high_quality(self, min_quality: float = 30.0, min_depth: int = 10) -> bool:
        """判断是否为高质量突变"""
        return (self.quality >= min_quality and 
                self.read_depth >= min_depth and 
                self.filter_status == 'PASS')
    
    def get_variant_type(self) -> str:
        """获取变异类型"""
        ref_len = len(self.reference)
        alt_len = len(self.alternate)
        
        if ref_len == 1 and alt_len == 1:
            return 'SNV'  # 单核苷酸变异
        elif ref_len > alt_len:
            return 'DEL'  # 缺失
        elif ref_len < alt_len:
            return 'INS'  # 插入
        else:
            return 'COMPLEX'  # 复杂变异


class VCFProcessor:
    """VCF文件处理器类 - 基于真实的VCF标准和SnpEFF注释"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化VCF处理器
        
        参数:
            config: 配置管理器实例
        """
        self.config = config
        self.mutations: List[MutationRecord] = []
        self.statistics = defaultdict(int)
        self.gene_annotations = {}  # 存储基因注释信息
        
        # SnpEFF效应类型映射（基于SnpEFF官方文档）
        self.snpeff_effects = {
            # 高影响效应
            'chromosome_number_variation': 'HIGH',
            'exon_loss_variant': 'HIGH',
            'frameshift_variant': 'HIGH',
            'rare_amino_acid_variant': 'HIGH',
            'splice_acceptor_variant': 'HIGH',
            'splice_donor_variant': 'HIGH',
            'start_lost': 'HIGH',
            'stop_gained': 'HIGH',
            'stop_lost': 'HIGH',
            'transcript_ablation': 'HIGH',
            
            # 中等影响效应
            '3_prime_UTR_truncation': 'MODERATE',
            '5_prime_UTR_truncation': 'MODERATE',
            'conservative_inframe_deletion': 'MODERATE',
            'conservative_inframe_insertion': 'MODERATE',
            'disruptive_inframe_deletion': 'MODERATE',
            'disruptive_inframe_insertion': 'MODERATE',
            'missense_variant': 'MODERATE',
            'protein_altering_variant': 'MODERATE',
            'regulatory_region_ablation': 'MODERATE',
            
            # 低影响效应
            '5_prime_UTR_premature_start_codon_gain_variant': 'LOW',
            'initiator_codon_variant': 'LOW',
            'splice_region_variant': 'LOW',
            'start_retained_variant': 'LOW',
            'stop_retained_variant': 'LOW',
            'synonymous_variant': 'LOW',
            
            # 修饰符效应
            '3_prime_UTR_variant': 'MODIFIER',
            '5_prime_UTR_variant': 'MODIFIER',
            'coding_sequence_variant': 'MODIFIER',
            'conserved_intergenic_variant': 'MODIFIER',
            'conserved_intron_variant': 'MODIFIER',
            'downstream_gene_variant': 'MODIFIER',
            'exon_variant': 'MODIFIER',
            'feature_elongation': 'MODIFIER',
            'feature_truncation': 'MODIFIER',
            'gene_variant': 'MODIFIER',
            'intergenic_region': 'MODIFIER',
            'intragenic_variant': 'MODIFIER',
            'intron_variant': 'MODIFIER',
            'mature_miRNA_variant': 'MODIFIER',
            'miRNA': 'MODIFIER',
            'NMD_transcript_variant': 'MODIFIER',
            'non_coding_transcript_exon_variant': 'MODIFIER',
            'non_coding_transcript_variant': 'MODIFIER',
            'regulatory_region_amplification': 'MODIFIER',
            'regulatory_region_variant': 'MODIFIER',
            'TF_binding_site_variant': 'MODIFIER',
            'TFBS_ablation': 'MODIFIER',
            'TFBS_amplification': 'MODIFIER',
            'transcript_amplification': 'MODIFIER',
            'transcript_variant': 'MODIFIER',
            'upstream_gene_variant': 'MODIFIER'
        }
        
        logger.info("VCF处理器初始化完成")
    
    def process_vcf(self, vcf_file: Optional[str] = None, 
                   sample_name: Optional[str] = None) -> List[MutationRecord]:
        """
        处理VCF文件，提取非同义突变
        
        参数:
            vcf_file: VCF文件路径（如果不提供，使用配置中的路径）
            sample_name: 样本名称（如果VCF包含多个样本）
            
        返回:
            突变记录列表
        """
        if vcf_file is None:
            vcf_file = self.config.input_files.vcf_file
        
        if not Path(vcf_file).exists():
            raise FileNotFoundError(f"VCF文件不存在: {vcf_file}")
        
        logger.info(f"开始处理VCF文件: {vcf_file}")
        
        # 清空之前的结果
        self.mutations.clear()
        self.statistics.clear()
        
        # 根据可用库选择处理方法
        if HAS_CYVCF2:
            self._process_with_cyvcf2(vcf_file, sample_name)
        else:
            self._process_with_pyvcf(vcf_file, sample_name)
        
        # 后处理和质量控制
        self._post_process_mutations()
        
        # 保存处理结果
        if self.config.output.save_intermediate_files:
            self._save_mutations()
        
        logger.info(f"VCF处理完成: 发现 {len(self.mutations)} 个非同义突变")
        logger.info(f"统计信息: {dict(self.statistics)}")
        
        return self.mutations
    
    def _process_with_cyvcf2(self, vcf_file: str, sample_name: Optional[str] = None) -> None:
        """使用cyvcf2库处理VCF文件（推荐，性能更好）"""
        try:
            vcf_reader = VCF(vcf_file, strict_gt=True)
            
            # 获取样本信息
            samples = vcf_reader.samples
            if sample_name and sample_name in samples:
                sample_index = samples.index(sample_name)
            elif len(samples) == 1:
                sample_index = 0
                sample_name = samples[0]
            else:
                sample_index = 0
                sample_name = samples[0] if samples else "sample"
                if len(samples) > 1:
                    logger.warning(f"多个样本，使用第一个样本: {sample_name}")
            
            logger.info(f"处理样本: {sample_name}")
            
            # 逐个处理变异
            variant_count = 0
            for variant in vcf_reader:
                variant_count += 1
                self.statistics['total_variants'] += 1
                
                try:
                    # 基本质量过滤
                    if not self._passes_basic_filters(variant):
                        continue
                    
                    # 提取SnpEFF注释
                    snpeff_annotations = self._extract_snpeff_annotations(variant)
                    
                    # 处理每个注释
                    for annotation in snpeff_annotations:
                        if self._is_non_synonymous(annotation):
                            mutation = self._create_mutation_record(variant, annotation, sample_index)
                            if mutation:
                                self.mutations.append(mutation)
                                self.statistics['non_synonymous_mutations'] += 1
                
                except Exception as e:
                    logger.warning(f"处理变异失败 {variant.CHROM}:{variant.POS}: {e}")
                    self.statistics['processing_errors'] += 1
                    continue
                
                # 定期输出进度
                if variant_count % 10000 == 0:
                    logger.info(f"已处理 {variant_count} 个变异")
            
            logger.info(f"总共处理了 {variant_count} 个变异")
            
        except Exception as e:
            logger.error(f"使用cyvcf2处理VCF文件失败: {e}")
            raise
    
    def _process_with_pyvcf(self, vcf_file: str, sample_name: Optional[str] = None) -> None:
        """使用pyvcf库处理VCF文件（备用方案）"""
        try:
            # 判断是否为压缩文件
            if vcf_file.endswith('.gz'):
                vcf_reader = pyvcf.Reader(open(vcf_file, 'rb'))
            else:
                vcf_reader = pyvcf.Reader(open(vcf_file, 'r'))
            
            # 获取样本信息
            samples = vcf_reader.samples
            if sample_name and sample_name in samples:
                target_sample = sample_name
            elif len(samples) == 1:
                target_sample = samples[0]
            else:
                target_sample = samples[0] if samples else None
                if len(samples) > 1:
                    logger.warning(f"多个样本，使用第一个样本: {target_sample}")
            
            logger.info(f"处理样本: {target_sample}")
            
            # 逐个处理变异
            variant_count = 0
            for record in vcf_reader:
                variant_count += 1
                self.statistics['total_variants'] += 1
                
                try:
                    # 基本质量过滤
                    if not self._passes_basic_filters_pyvcf(record):
                        continue
                    
                    # 提取SnpEFF注释
                    snpeff_annotations = self._extract_snpeff_annotations_pyvcf(record)
                    
                    # 处理每个注释
                    for annotation in snpeff_annotations:
                        if self._is_non_synonymous(annotation):
                            mutation = self._create_mutation_record_pyvcf(record, annotation, target_sample)
                            if mutation:
                                self.mutations.append(mutation)
                                self.statistics['non_synonymous_mutations'] += 1
                
                except Exception as e:
                    logger.warning(f"处理变异失败 {record.CHROM}:{record.POS}: {e}")
                    self.statistics['processing_errors'] += 1
                    continue
                
                # 定期输出进度
                if variant_count % 10000 == 0:
                    logger.info(f"已处理 {variant_count} 个变异")
            
            logger.info(f"总共处理了 {variant_count} 个变异")
            
        except Exception as e:
            logger.error(f"使用pyvcf处理VCF文件失败: {e}")
            raise
    
    def _passes_basic_filters(self, variant) -> bool:
        """基本质量过滤（cyvcf2版本）"""
        # 质量分数过滤
        if variant.QUAL is not None and variant.QUAL < self.config.vcf_filter.min_quality:
            self.statistics['low_quality_filtered'] += 1
            return False
        
        # 过滤状态检查
        if variant.FILTER is not None and variant.FILTER != 'PASS':
            self.statistics['filter_failed'] += 1
            return False
        
        # 深度过滤
        if hasattr(variant, 'INFO') and 'DP' in variant.INFO:
            if variant.INFO['DP'] < self.config.vcf_filter.min_read_depth:
                self.statistics['low_depth_filtered'] += 1
                return False
        
        return True
    
    def _passes_basic_filters_pyvcf(self, record) -> bool:
        """基本质量过滤（pyvcf版本）"""
        # 质量分数过滤
        if record.QUAL is not None and record.QUAL < self.config.vcf_filter.min_quality:
            self.statistics['low_quality_filtered'] += 1
            return False
        
        # 过滤状态检查
        if record.FILTER and 'PASS' not in record.FILTER:
            self.statistics['filter_failed'] += 1
            return False
        
        # 深度过滤
        if hasattr(record, 'INFO') and 'DP' in record.INFO:
            if record.INFO['DP'] < self.config.vcf_filter.min_read_depth:
                self.statistics['low_depth_filtered'] += 1
                return False
        
        return True
    
    def _extract_snpeff_annotations(self, variant) -> List[Dict[str, str]]:
        """提取SnpEFF注释信息（cyvcf2版本）"""
        annotations = []
        
        # SnpEFF注释通常在ANN字段中
        if 'ANN' in variant.INFO:
            ann_field = variant.INFO['ANN']
            if isinstance(ann_field, (list, tuple)):
                ann_entries = ann_field
            else:
                ann_entries = [ann_field]
            
            for ann_entry in ann_entries:
                annotation = self._parse_snpeff_annotation(ann_entry)
                if annotation:
                    annotations.append(annotation)
        
        # 也检查EFF字段（旧版本SnpEFF）
        elif 'EFF' in variant.INFO:
            eff_field = variant.INFO['EFF']
            if isinstance(eff_field, (list, tuple)):
                eff_entries = eff_field
            else:
                eff_entries = [eff_field]
            
            for eff_entry in eff_entries:
                annotation = self._parse_snpeff_effect(eff_entry)
                if annotation:
                    annotations.append(annotation)
        
        return annotations
    
    def _extract_snpeff_annotations_pyvcf(self, record) -> List[Dict[str, str]]:
        """提取SnpEFF注释信息（pyvcf版本）"""
        annotations = []
        
        # SnpEFF注释通常在ANN字段中
        if 'ANN' in record.INFO:
            ann_field = record.INFO['ANN']
            if isinstance(ann_field, (list, tuple)):
                ann_entries = ann_field
            else:
                ann_entries = [ann_field]
            
            for ann_entry in ann_entries:
                annotation = self._parse_snpeff_annotation(ann_entry)
                if annotation:
                    annotations.append(annotation)
        
        # 也检查EFF字段（旧版本SnpEFF）
        elif 'EFF' in record.INFO:
            eff_field = record.INFO['EFF']
            if isinstance(eff_field, (list, tuple)):
                eff_entries = eff_field
            else:
                eff_entries = [eff_field]
            
            for eff_entry in eff_entries:
                annotation = self._parse_snpeff_effect(eff_entry)
                if annotation:
                    annotations.append(annotation)
        
        return annotations
    
    def _parse_snpeff_annotation(self, ann_string: str) -> Optional[Dict[str, str]]:
        """
        解析SnpEFF ANN字段
        
        ANN字段格式（SnpEFF 4.3+）：
        Allele|Annotation|Annotation_Impact|Gene_Name|Gene_ID|Feature_Type|Feature_ID|
        Transcript_BioType|Rank|HGVS.c|HGVS.p|cDNA.pos/cDNA.length|CDS.pos/CDS.length|
        AA.pos/AA.length|Distance|ERRORS/WARNINGS/INFO
        """
        try:
            fields = ann_string.split('|')
            if len(fields) < 8:
                return None
            
            annotation = {
                'allele': fields[0],
                'effect_type': fields[1],
                'effect_impact': fields[2],
                'gene_name': fields[3],
                'gene_id': fields[4],
                'feature_type': fields[5],
                'feature_id': fields[6],  # 通常是转录本ID
                'transcript_biotype': fields[7] if len(fields) > 7 else '',
                'rank': fields[8] if len(fields) > 8 else '',
                'hgvs_c': fields[9] if len(fields) > 9 else '',
                'hgvs_p': fields[10] if len(fields) > 10 else '',
                'cdna_pos': fields[11] if len(fields) > 11 else '',
                'cds_pos': fields[12] if len(fields) > 12 else '',
                'aa_pos': fields[13] if len(fields) > 13 else '',
                'distance': fields[14] if len(fields) > 14 else '',
                'messages': fields[15] if len(fields) > 15 else ''
            }
            
            return annotation
            
        except Exception as e:
            logger.debug(f"解析SnpEFF注释失败: {e}")
            return None
    
    def _parse_snpeff_effect(self, eff_string: str) -> Optional[Dict[str, str]]:
        """
        解析SnpEFF EFF字段（旧格式）
        
        EFF字段格式：
        Effect(Effect_Impact|Functional_Class|Codon_Change|Amino_Acid_Change|
        Amino_Acid_length|Gene_Name|Transcript_BioType|Gene_Coding|Transcript_ID|
        Exon_Rank|Genotype_Number[|ERRORS|WARNINGS])
        """
        try:
            # 分离效应类型和详细信息
            if '(' not in eff_string:
                return None
            
            effect_type = eff_string.split('(')[0]
            details = eff_string.split('(')[1].rstrip(')')
            
            fields = details.split('|')
            
            annotation = {
                'effect_type': effect_type,
                'effect_impact': fields[0] if len(fields) > 0 else '',
                'functional_class': fields[1] if len(fields) > 1 else '',
                'codon_change': fields[2] if len(fields) > 2 else '',
                'amino_acid_change': fields[3] if len(fields) > 3 else '',
                'aa_length': fields[4] if len(fields) > 4 else '',
                'gene_name': fields[5] if len(fields) > 5 else '',
                'transcript_biotype': fields[6] if len(fields) > 6 else '',
                'gene_coding': fields[7] if len(fields) > 7 else '',
                'transcript_id': fields[8] if len(fields) > 8 else '',
                'exon_rank': fields[9] if len(fields) > 9 else ''
            }
            
            # 从效应类型推断影响等级
            if annotation['effect_impact'] == '':
                annotation['effect_impact'] = self.snpeff_effects.get(effect_type, 'UNKNOWN')
            
            return annotation
            
        except Exception as e:
            logger.debug(f"解析SnpEFF效应失败: {e}")
            return None
    
    def _is_non_synonymous(self, annotation: Dict[str, str]) -> bool:
        """判断是否为非同义突变"""
        effect_type = annotation.get('effect_type', '').lower()
        effect_impact = annotation.get('effect_impact', '').upper()
        
        # 检查配置中定义的非同义效应类型
        non_synonymous_effects = [eff.lower() for eff in self.config.vcf_filter.non_synonymous_effects]
        
        # 检查效应类型
        if any(eff in effect_type for eff in non_synonymous_effects):
            return True
        
        # 检查影响等级
        if effect_impact in self.config.vcf_filter.impact_levels:
            return True
        
        # 排除明确的同义突变
        synonymous_effects = ['synonymous_variant', 'synonymous', 'silent']
        if any(syn in effect_type for syn in synonymous_effects):
            return False
        
        return False
    
    def _create_mutation_record(self, variant, annotation: Dict[str, str], sample_index: int) -> Optional[MutationRecord]:
        """创建突变记录（cyvcf2版本）"""
        try:
            # 基本信息
            mutation = MutationRecord(
                chromosome=str(variant.CHROM),
                position=int(variant.POS),
                reference=str(variant.REF),
                alternate=str(variant.ALT[0]) if variant.ALT else '',
                quality=float(variant.QUAL) if variant.QUAL is not None else 0.0,
                filter_status=str(variant.FILTER) if variant.FILTER else 'PASS'
            )
            
            # SnpEFF注释信息
            mutation.effect_type = annotation.get('effect_type', '')
            mutation.effect_impact = annotation.get('effect_impact', '')
            mutation.gene_id = annotation.get('gene_id', '')
            mutation.gene_name = annotation.get('gene_name', '')
            mutation.transcript_id = annotation.get('feature_id', '')
            
            # 蛋白质变化信息
            hgvs_p = annotation.get('hgvs_p', '')
            if hgvs_p:
                mutation.protein_change = hgvs_p
                # 提取氨基酸位置
                aa_pos_match = re.search(r'p\..*?(\d+)', hgvs_p)
                if aa_pos_match:
                    mutation.amino_acid_position = int(aa_pos_match.group(1))
            
            # 密码子变化
            mutation.codon_change = annotation.get('hgvs_c', '')
            
            # 样本信息
            if sample_index < len(variant.gt_types):
                gt = variant.gt_types[sample_index]
                if gt == 0:  # 0/0
                    mutation.genotype = '0/0'
                elif gt == 1:  # 0/1
                    mutation.genotype = '0/1'
                elif gt == 3:  # 1/1
                    mutation.genotype = '1/1'
                else:
                    mutation.genotype = './.'
            
            # 读取深度
            if hasattr(variant, 'gt_depths') and sample_index < len(variant.gt_depths):
                mutation.read_depth = variant.gt_depths[sample_index]
            
            # 等位基因深度
            if hasattr(variant, 'gt_ref_depths') and hasattr(variant, 'gt_alt_depths'):
                if (sample_index < len(variant.gt_ref_depths) and 
                    sample_index < len(variant.gt_alt_depths)):
                    mutation.allele_depth = [
                        variant.gt_ref_depths[sample_index],
                        variant.gt_alt_depths[sample_index]
                    ]
            
            # 等位基因频率
            if mutation.allele_depth and sum(mutation.allele_depth) > 0:
                mutation.allele_frequency = mutation.allele_depth[1] / sum(mutation.allele_depth)
            
            # 功能预测注释（如果有的话）
            self._extract_functional_predictions(variant, mutation)
            
            return mutation
            
        except Exception as e:
            logger.warning(f"创建突变记录失败: {e}")
            return None
    
    def _create_mutation_record_pyvcf(self, record, annotation: Dict[str, str], sample_name: str) -> Optional[MutationRecord]:
        """创建突变记录（pyvcf版本）"""
        try:
            # 基本信息
            mutation = MutationRecord(
                chromosome=str(record.CHROM),
                position=int(record.POS),
                reference=str(record.REF),
                alternate=str(record.ALT[0]) if record.ALT else '',
                quality=float(record.QUAL) if record.QUAL is not None else 0.0,
                filter_status=str(record.FILTER[0]) if record.FILTER else 'PASS'
            )
            
            # SnpEFF注释信息
            mutation.effect_type = annotation.get('effect_type', '')
            mutation.effect_impact = annotation.get('effect_impact', '')
            mutation.gene_id = annotation.get('gene_id', '')
            mutation.gene_name = annotation.get('gene_name', '')
            mutation.transcript_id = annotation.get('feature_id', annotation.get('transcript_id', ''))
            
            # 蛋白质变化信息
            hgvs_p = annotation.get('hgvs_p', annotation.get('amino_acid_change', ''))
            if hgvs_p:
                mutation.protein_change = hgvs_p
                # 提取氨基酸位置
                aa_pos_match = re.search(r'(\d+)', hgvs_p)
                if aa_pos_match:
                    mutation.amino_acid_position = int(aa_pos_match.group(1))
            
            # 密码子变化
            mutation.codon_change = annotation.get('hgvs_c', annotation.get('codon_change', ''))
            
            # 样本信息
            if sample_name and hasattr(record, 'samples'):
                sample = record.genotype(sample_name)
                if sample:
                    mutation.genotype = str(sample['GT']) if 'GT' in sample.data._fields else ''
                    
                    # 读取深度
                    if 'DP' in sample.data._fields:
                        mutation.read_depth = sample['DP'] if sample['DP'] is not None else 0
                    
                    # 等位基因深度
                    if 'AD' in sample.data._fields and sample['AD']:
                        mutation.allele_depth = list(sample['AD'])
                        if sum(mutation.allele_depth) > 0:
                            mutation.allele_frequency = mutation.allele_depth[1] / sum(mutation.allele_depth)
            
            return mutation
            
        except Exception as e:
            logger.warning(f"创建突变记录失败: {e}")
            return None
    
    def _extract_functional_predictions(self, variant, mutation: MutationRecord) -> None:
        """提取功能预测信息"""
        try:
            # SIFT预测
            if 'SIFT' in variant.INFO:
                sift_info = variant.INFO['SIFT']
                if '(' in str(sift_info):
                    # 格式：prediction(score)
                    parts = str(sift_info).split('(')
                    mutation.sift_prediction = parts[0]
                    if len(parts) > 1:
                        score_str = parts[1].rstrip(')')
                        try:
                            mutation.sift_score = float(score_str)
                        except ValueError:
                            pass
                else:
                    mutation.sift_prediction = str(sift_info)
            
            # PolyPhen预测
            if 'PolyPhen' in variant.INFO:
                polyphen_info = variant.INFO['PolyPhen']
                if '(' in str(polyphen_info):
                    parts = str(polyphen_info).split('(')
                    mutation.polyphen_prediction = parts[0]
                    if len(parts) > 1:
                        score_str = parts[1].rstrip(')')
                        try:
                            mutation.polyphen_score = float(score_str)
                        except ValueError:
                            pass
                else:
                    mutation.polyphen_prediction = str(polyphen_info)
            
            # CADD分数
            if 'CADD_PHRED' in variant.INFO:
                try:
                    mutation.cadd_score = float(variant.INFO['CADD_PHRED'])
                except (ValueError, TypeError):
                    pass
            elif 'CADD' in variant.INFO:
                try:
                    mutation.cadd_score = float(variant.INFO['CADD'])
                except (ValueError, TypeError):
                    pass
                    
        except Exception as e:
            logger.debug(f"提取功能预测信息失败: {e}")
    
    def _post_process_mutations(self) -> None:
        """后处理突变数据"""
        logger.info("开始后处理突变数据...")
        
        # 应用高级过滤
        initial_count = len(self.mutations)
        
        # 过滤低质量突变
        self.mutations = [mut for mut in self.mutations 
                         if mut.is_high_quality(
                             self.config.vcf_filter.min_quality,
                             self.config.vcf_filter.min_read_depth
                         )]
        
        filtered_count = initial_count - len(self.mutations)
        if filtered_count > 0:
            logger.info(f"过滤掉 {filtered_count} 个低质量突变")
            self.statistics['quality_filtered'] = filtered_count
        
        # 应用等位基因频率过滤
        if self.config.vcf_filter.min_allele_frequency > 0:
            af_filtered = [mut for mut in self.mutations 
                          if mut.allele_frequency >= self.config.vcf_filter.min_allele_frequency]
            af_filtered_count = len(self.mutations) - len(af_filtered)
            if af_filtered_count > 0:
                logger.info(f"过滤掉 {af_filtered_count} 个低频率突变")
                self.statistics['allele_frequency_filtered'] = af_filtered_count
                self.mutations = af_filtered
        
        # 去重（基于基因组位置和变异）
        unique_mutations = {}
        for mut in self.mutations:
            key = mut.get_mutation_key()
            if key not in unique_mutations:
                unique_mutations[key] = mut
            else:
                # 保留质量更高的突变
                if mut.quality > unique_mutations[key].quality:
                    unique_mutations[key] = mut
        
        duplicate_count = len(self.mutations) - len(unique_mutations)
        if duplicate_count > 0:
            logger.info(f"去除 {duplicate_count} 个重复突变")
            self.statistics['duplicates_removed'] = duplicate_count
        
        self.mutations = list(unique_mutations.values())
        
        # 排序（按染色体和位置）
        self.mutations.sort(key=lambda x: (x.chromosome, x.position))
        
        logger.info(f"后处理完成，保留 {len(self.mutations)} 个高质量非同义突变")
    
    def _save_mutations(self) -> None:
        """保存突变数据"""
        if not self.mutations:
            logger.warning("没有突变数据需要保存")
            return
        
        # 转换为DataFrame
        mutation_data = []
        for mutation in self.mutations:
            mut_dict = mutation.to_dict()
            # 将列表字段转换为字符串
            mut_dict['allele_depth'] = ','.join(map(str, mutation.allele_depth))
            mut_dict['population_frequency'] = json.dumps(mutation.population_frequency)
            mut_dict['additional_annotations'] = json.dumps(mutation.additional_annotations)
            mutation_data.append(mut_dict)
        
        df = pd.DataFrame(mutation_data)
        
        # 保存到CSV文件
        csv_path = self.config.get_output_path('mutations', 'mutations_detailed.csv')
        df.to_csv(csv_path, index=False)
        logger.info(f"详细突变数据已保存到: {csv_path}")
        
        # 保存到Excel文件（包含多个sheet）
        excel_path = self.config.get_output_path('mutations', 'mutations_detailed.xlsx')
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # 主要突变数据
            df.to_excel(writer, sheet_name='Mutations', index=False)
            
            # 统计摘要
            stats_data = [{'统计项': k, '数值': v} for k, v in self.statistics.items()]
            if stats_data:
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='Statistics', index=False)
            
            # 基因突变汇总
            gene_summary = self.get_gene_mutation_summary()
            if not gene_summary.empty:
                gene_summary.to_excel(writer, sheet_name='Gene Summary', index=False)
        
        logger.info(f"Excel格式突变数据已保存到: {excel_path}")
        
        # 保存JSON格式（便于程序读取）
        json_path = self.config.get_output_path('mutations', 'mutations_detailed.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump([mut.to_dict() for mut in self.mutations], f, ensure_ascii=False, indent=2)
        logger.info(f"JSON格式突变数据已保存到: {json_path}")
    
    def get_mutation_statistics(self) -> Dict[str, Any]:
        """获取突变统计信息"""
        if not self.mutations:
            return {}
        
        stats = dict(self.statistics)
        
        # 基本统计
        stats.update({
            'final_mutation_count': len(self.mutations),
            'unique_genes': len(set(mut.gene_id for mut in self.mutations if mut.gene_id)),
            'chromosomes': len(set(mut.chromosome for mut in self.mutations))
        })
        
        # 影响等级分布
        impact_counts = Counter(mut.effect_impact for mut in self.mutations)
        stats['impact_distribution'] = dict(impact_counts)
        
        # 变异类型分布
        variant_type_counts = Counter(mut.get_variant_type() for mut in self.mutations)
        stats['variant_type_distribution'] = dict(variant_type_counts)
        
        # 效应类型分布
        effect_counts = Counter(mut.effect_type for mut in self.mutations)
        stats['effect_type_distribution'] = dict(effect_counts)
        
        # 质量统计
        qualities = [mut.quality for mut in self.mutations if mut.quality > 0]
        if qualities:
            stats['quality_stats'] = {
                'mean': np.mean(qualities),
                'median': np.median(qualities),
                'min': np.min(qualities),
                'max': np.max(qualities)
            }
        
        # 深度统计
        depths = [mut.read_depth for mut in self.mutations if mut.read_depth > 0]
        if depths:
            stats['depth_stats'] = {
                'mean': np.mean(depths),
                'median': np.median(depths),
                'min': np.min(depths),
                'max': np.max(depths)
            }
        
        return stats
    
    def get_gene_mutation_summary(self) -> pd.DataFrame:
        """获取基因突变汇总"""
        if not self.mutations:
            return pd.DataFrame()
        
        # 按基因统计突变
        gene_stats = defaultdict(lambda: {
            'mutation_count': 0,
            'high_impact': 0,
            'moderate_impact': 0,
            'missense': 0,
            'nonsense': 0,
            'frameshift': 0,
            'effects': set()
        })
        
        for mutation in self.mutations:
            if not mutation.gene_id:
                continue
            
            gene_id = mutation.gene_id
            stats = gene_stats[gene_id]
            
            stats['mutation_count'] += 1
            stats['effects'].add(mutation.effect_type)
            
            # 按影响等级分类
            if mutation.effect_impact == 'HIGH':
                stats['high_impact'] += 1
            elif mutation.effect_impact == 'MODERATE':
                stats['moderate_impact'] += 1
            
            # 按效应类型分类
            if 'missense' in mutation.effect_type:
                stats['missense'] += 1
            elif 'stop_gained' in mutation.effect_type or 'nonsense' in mutation.effect_type:
                stats['nonsense'] += 1
            elif 'frameshift' in mutation.effect_type:
                stats['frameshift'] += 1
        
        # 转换为DataFrame
        rows = []
        for gene_id, stats in gene_stats.items():
            rows.append({
                '基因ID': gene_id,
                '基因名称': self.gene_annotations.get(gene_id, {}).get('name', gene_id),
                '突变总数': stats['mutation_count'],
                '高影响突变': stats['high_impact'],
                '中等影响突变': stats['moderate_impact'],
                '错义突变': stats['missense'],
                '无义突变': stats['nonsense'],
                '移码突变': stats['frameshift'],
                '效应类型': ', '.join(sorted(stats['effects']))
            })
        
        df = pd.DataFrame(rows)
        df = df.sort_values('突变总数', ascending=False)
        
        return df
    
    def load_gene_annotations(self, gff_file: Optional[str] = None) -> None:
        """
        加载基因注释信息（从GFF文件）
        
        参数:
            gff_file: GFF文件路径
        """
        if gff_file is None:
            gff_file = self.config.input_files.gff_file
        
        if not gff_file or not Path(gff_file).exists():
            logger.warning(f"GFF文件不存在: {gff_file}")
            return
        
        logger.info(f"加载基因注释: {gff_file}")
        
        # 清空现有注释
        self.gene_annotations.clear()
        
        try:
            # 判断是否为压缩文件
            if gff_file.endswith('.gz'):
                file_handle = gzip.open(gff_file, 'rt')
            else:
                file_handle = open(gff_file, 'r')
            
            with file_handle as f:
                for line_num, line in enumerate(f, 1):
                    if line.startswith('#') or not line.strip():
                        continue
                    
                    try:
                        fields = line.strip().split('\t')
                        if len(fields) < 9:
                            continue
                        
                        feature_type = fields[2]
                        if feature_type not in ['gene', 'CDS', 'mRNA']:
                            continue
                        
                        # 解析属性字段
                        attributes = {}
                        for attr in fields[8].split(';'):
                            if '=' in attr:
                                key, value = attr.split('=', 1)
                                attributes[key.strip()] = value.strip()
                        
                        # 提取基因信息
                        gene_id = (attributes.get('ID') or 
                                  attributes.get('gene_id') or 
                                  attributes.get('locus_tag'))
                        
                        if gene_id and feature_type == 'gene':
                            self.gene_annotations[gene_id] = {
                                'chromosome': fields[0],
                                'start': int(fields[3]),
                                'end': int(fields[4]),
                                'strand': fields[6],
                                'name': (attributes.get('Name') or 
                                        attributes.get('gene') or 
                                        gene_id),
                                'product': (attributes.get('product') or 
                                           attributes.get('description') or ''),
                                'attributes': attributes
                            }
                    
                    except Exception as e:
                        logger.warning(f"解析GFF第{line_num}行失败: {e}")
                        continue
            
            logger.info(f"成功加载 {len(self.gene_annotations)} 个基因注释")
            
        except Exception as e:
            logger.error(f"加载GFF文件失败: {e}")


# 便捷函数
def process_vcf_file(vcf_file: str, config: ConfigManager) -> List[MutationRecord]:
    """
    处理VCF文件的便捷函数
    
    参数:
        vcf_file: VCF文件路径
        config: 配置管理器
        
    返回:
        突变记录列表
    """
    processor = VCFProcessor(config)
    return processor.process_vcf(vcf_file)


if __name__ == '__main__':
    # 测试代码
    import argparse
    from config_manager import ConfigManager
    
    parser = argparse.ArgumentParser(description='VCF文件处理器')
    parser.add_argument('vcf_file', help='输入VCF文件路径')
    parser.add_argument('--config', default='config/default_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--sample', help='样本名称（多样本VCF）')
    parser.add_argument('--gff', help='基因注释GFF文件')
    parser.add_argument('--output', help='输出目录')
    
    args = parser.parse_args()
    
    # 加载配置
    config = ConfigManager(args.config)
    
    # 设置输出目录
    if args.output:
        config.output.results_dir = args.output
    
    # 处理VCF
    processor = VCFProcessor(config)
    
    # 加载基因注释（如果提供）
    if args.gff:
        processor.load_gene_annotations(args.gff)
    
    # 处理VCF文件
    mutations = processor.process_vcf(args.vcf_file, args.sample)
    
    print(f"\n处理完成！发现 {len(mutations)} 个非同义突变")
    
    # 显示统计信息
    stats = processor.get_mutation_statistics()
    print("\n突变统计:")
    for key, value in stats.items():
        if isinstance(value, dict):
            print(f"  {key}:")
            for k, v in value.items():
                print(f"    {k}: {v}")
        else:
            print(f"  {key}: {value}")
    
    # 显示基因突变摘要
    summary = processor.get_gene_mutation_summary()
    if not summary.empty:
        print("\n基因突变摘要（前10个）:")
        print(summary.head(10).to_string(index=False))