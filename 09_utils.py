#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具类模块 - 梭菌生物信息学工作流程
作者：生物信息学分析团队
版本：3.0.0
描述：提供通用的工具函数和辅助类，支持各个分析模块
"""

import logging
import os
import sys
import time
import hashlib
import json
import pickle
import gzip
import shutil
import tempfile
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple, Callable
from functools import wraps, lru_cache
from datetime import datetime, timedelta
import subprocess
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import threading
import signal

import pandas as pd
import numpy as np
from Bio import SeqIO
import requests

logger = logging.getLogger(__name__)


class Timer:
    """计时器类"""
    
    def __init__(self, name: str = "操作"):
        self.name = name
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        logger.info(f"开始{self.name}...")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        elapsed = self.end_time - self.start_time
        logger.info(f"{self.name}完成，耗时: {self.format_time(elapsed)}")
    
    @staticmethod
    def format_time(seconds: float) -> str:
        """格式化时间显示"""
        if seconds < 60:
            return f"{seconds:.2f}秒"
        elif seconds < 3600:
            minutes, secs = divmod(seconds, 60)
            return f"{int(minutes)}分{secs:.1f}秒"
        else:
            hours, remainder = divmod(seconds, 3600)
            minutes, secs = divmod(remainder, 60)
            return f"{int(hours)}时{int(minutes)}分{secs:.0f}秒"


def timeit(func: Callable) -> Callable:
    """计时装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        elapsed = time.time() - start_time
        logger.debug(f"函数 {func.__name__} 执行时间: {Timer.format_time(elapsed)}")
        return result
    return wrapper


def retry(max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """重试装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            attempts = 0
            current_delay = delay
            
            while attempts < max_attempts:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    attempts += 1
                    if attempts >= max_attempts:
                        logger.error(f"函数 {func.__name__} 重试{max_attempts}次后仍然失败: {e}")
                        raise
                    
                    logger.warning(f"函数 {func.__name__} 第{attempts}次尝试失败: {e}，{current_delay}秒后重试...")
                    time.sleep(current_delay)
                    current_delay *= backoff
            
            return None
        return wrapper
    return decorator


class ProgressBar:
    """简单的进度条类"""
    
    def __init__(self, total: int, description: str = "处理中", width: int = 50):
        self.total = total
        self.current = 0
        self.description = description
        self.width = width
        self.start_time = time.time()
    
    def update(self, amount: int = 1):
        """更新进度"""
        self.current += amount
        self._display()
    
    def _display(self):
        """显示进度条"""
        if self.total == 0:
            return
        
        progress = self.current / self.total
        filled = int(self.width * progress)
        
        elapsed = time.time() - self.start_time
        if progress > 0:
            eta = elapsed / progress * (1 - progress)
            eta_str = Timer.format_time(eta)
        else:
            eta_str = "未知"
        
        bar = '█' * filled + '░' * (self.width - filled)
        percent = progress * 100
        
        print(f"\r{self.description}: |{bar}| {percent:.1f}% ({self.current}/{self.total}) ETA: {eta_str}", 
              end='', flush=True)
        
        if self.current >= self.total:
            print()  # 完成后换行


class FileManager:
    """文件管理工具类"""
    
    @staticmethod
    def ensure_dir(path: Union[str, Path]) -> Path:
        """确保目录存在"""
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @staticmethod
    def safe_remove(path: Union[str, Path]) -> bool:
        """安全删除文件或目录"""
        try:
            path = Path(path)
            if path.is_file():
                path.unlink()
            elif path.is_dir():
                shutil.rmtree(path)
            return True
        except Exception as e:
            logger.warning(f"删除文件/目录失败 {path}: {e}")
            return False
    
    @staticmethod
    def get_file_hash(file_path: Union[str, Path], algorithm: str = 'md5') -> str:
        """计算文件哈希值"""
        hash_func = hashlib.new(algorithm)
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_func.update(chunk)
        
        return hash_func.hexdigest()
    
    @staticmethod
    def copy_with_progress(src: Path, dst: Path, chunk_size: int = 1024*1024) -> None:
        """带进度显示的文件复制"""
        src = Path(src)
        dst = Path(dst)
        
        file_size = src.stat().st_size
        copied = 0
        
        with open(src, 'rb') as src_file, open(dst, 'wb') as dst_file:
            progress = ProgressBar(file_size, f"复制 {src.name}")
            
            while True:
                chunk = src_file.read(chunk_size)
                if not chunk:
                    break
                
                dst_file.write(chunk)
                copied += len(chunk)
                progress.update(len(chunk))
    
    @staticmethod
    def compress_file(input_file: Path, output_file: Optional[Path] = None) -> Path:
        """压缩文件"""
        if output_file is None:
            output_file = input_file.with_suffix(input_file.suffix + '.gz')
        
        with open(input_file, 'rb') as f_in:
            with gzip.open(output_file, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        
        return output_file
    
    @staticmethod
    def decompress_file(input_file: Path, output_file: Optional[Path] = None) -> Path:
        """解压缩文件"""
        if output_file is None:
            output_file = input_file.with_suffix('')
        
        with gzip.open(input_file, 'rb') as f_in:
            with open(output_file, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        
        return output_file


class DataCache:
    """数据缓存管理器"""
    
    def __init__(self, cache_dir: Union[str, Path] = '.cache', max_size_mb: int = 1000):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.max_size_bytes = max_size_mb * 1024 * 1024
    
    def _get_cache_path(self, key: str) -> Path:
        """获取缓存文件路径"""
        safe_key = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{safe_key}.cache"
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        cache_path = self._get_cache_path(key)
        
        if not cache_path.exists():
            return None
        
        try:
            with open(cache_path, 'rb') as f:
                data = pickle.load(f)
            
            # 更新访问时间
            cache_path.touch()
            logger.debug(f"缓存命中: {key}")
            return data
            
        except Exception as e:
            logger.warning(f"读取缓存失败 {key}: {e}")
            self.safe_remove(cache_path)
            return None
    
    def set(self, key: str, data: Any) -> None:
        """设置缓存数据"""
        cache_path = self._get_cache_path(key)
        
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(data, f)
            
            logger.debug(f"数据已缓存: {key}")
            
            # 检查缓存大小限制
            self._cleanup_if_needed()
            
        except Exception as e:
            logger.warning(f"缓存数据失败 {key}: {e}")
    
    def _cleanup_if_needed(self) -> None:
        """如果需要则清理缓存"""
        total_size = sum(f.stat().st_size for f in self.cache_dir.glob('*.cache'))
        
        if total_size > self.max_size_bytes:
            # 按访问时间排序，删除最老的文件
            cache_files = sorted(
                self.cache_dir.glob('*.cache'),
                key=lambda f: f.stat().st_atime
            )
            
            removed_size = 0
            for cache_file in cache_files:
                if total_size - removed_size < self.max_size_bytes * 0.8:
                    break
                
                removed_size += cache_file.stat().st_size
                cache_file.unlink()
            
            logger.info(f"清理缓存，释放 {removed_size / 1024 / 1024:.1f} MB")
    
    def clear(self) -> None:
        """清空所有缓存"""
        for cache_file in self.cache_dir.glob('*.cache'):
            cache_file.unlink()
        logger.info("已清空所有缓存")


class ConfigValidator:
    """配置验证器"""
    
    @staticmethod
    def validate_file_path(path: str, must_exist: bool = True) -> bool:
        """验证文件路径"""
        if not path:
            return False
        
        path_obj = Path(path)
        
        if must_exist:
            return path_obj.exists()
        else:
            # 检查父目录是否存在且可写
            parent = path_obj.parent
            return parent.exists() and os.access(parent, os.W_OK)
    
    @staticmethod
    def validate_numeric_range(value: Union[int, float], min_val: float = None, 
                              max_val: float = None) -> bool:
        """验证数值范围"""
        if min_val is not None and value < min_val:
            return False
        if max_val is not None and value > max_val:
            return False
        return True
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """验证邮箱格式"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_url(url: str) -> bool:
        """验证URL格式"""
        try:
            from urllib.parse import urlparse
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False


class SystemInfo:
    """系统信息获取器"""
    
    @staticmethod
    def get_system_info() -> Dict[str, Any]:
        """获取系统信息"""
        import platform
        import psutil
        
        info = {
            'platform': platform.platform(),
            'python_version': platform.python_version(),
            'cpu_count': mp.cpu_count(),
            'memory_total_gb': psutil.virtual_memory().total / (1024**3),
            'memory_available_gb': psutil.virtual_memory().available / (1024**3),
            'disk_usage': {}
        }
        
        # 磁盘使用情况
        for partition in psutil.disk_partitions():
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                info['disk_usage'][partition.mountpoint] = {
                    'total_gb': usage.total / (1024**3),
                    'used_gb': usage.used / (1024**3),
                    'free_gb': usage.free / (1024**3)
                }
            except:
                continue
        
        return info
    
    @staticmethod
    def check_dependencies() -> Dict[str, Dict[str, Any]]:
        """检查依赖包"""
        required_packages = {
            'pandas': '>=1.3.0',
            'numpy': '>=1.20.0',
            'scipy': '>=1.7.0',
            'matplotlib': '>=3.3.0',
            'seaborn': '>=0.11.0',
            'biopython': '>=1.78',
            'cyvcf2': '>=0.30.0',
            'cobra': '>=0.25.0',
            'plotly': '>=5.0.0',
            'statsmodels': '>=0.12.0'
        }
        
        dependency_status = {}
        
        for package, min_version in required_packages.items():
            try:
                module = __import__(package)
                version = getattr(module, '__version__', 'unknown')
                
                dependency_status[package] = {
                    'installed': True,
                    'version': version,
                    'min_required': min_version,
                    'status': 'OK'
                }
            except ImportError:
                dependency_status[package] = {
                    'installed': False,
                    'version': None,
                    'min_required': min_version,
                    'status': 'MISSING'
                }
        
        return dependency_status


class ParallelProcessor:
    """并行处理器"""
    
    def __init__(self, n_jobs: int = None, backend: str = 'threading'):
        self.n_jobs = n_jobs or min(mp.cpu_count(), 8)
        self.backend = backend
    
    def map(self, func: Callable, items: List[Any], progress: bool = True) -> List[Any]:
        """并行映射函数"""
        if len(items) == 1 or self.n_jobs == 1:
            # 单任务或单线程，直接执行
            if progress:
                results = []
                pbar = ProgressBar(len(items), "处理中")
                for item in items:
                    results.append(func(item))
                    pbar.update()
                return results
            else:
                return [func(item) for item in items]
        
        # 并行执行
        if self.backend == 'threading':
            executor_class = ThreadPoolExecutor
        else:
            executor_class = ProcessPoolExecutor
        
        results = []
        
        with executor_class(max_workers=self.n_jobs) as executor:
            if progress:
                futures = [executor.submit(func, item) for item in items]
                pbar = ProgressBar(len(futures), "并行处理中")
                
                for future in futures:
                    results.append(future.result())
                    pbar.update()
            else:
                futures = [executor.submit(func, item) for item in items]
                results = [future.result() for future in futures]
        
        return results


class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, threshold_mb: int = 8000):
        self.threshold_bytes = threshold_mb * 1024 * 1024
        self.monitoring = False
        self.thread = None
    
    def start_monitoring(self, callback: Callable = None):
        """开始监控内存使用"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.thread = threading.Thread(target=self._monitor_loop, args=(callback,))
        self.thread.daemon = True
        self.thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.thread:
            self.thread.join()
    
    def _monitor_loop(self, callback: Callable = None):
        """监控循环"""
        import psutil
        
        while self.monitoring:
            memory_usage = psutil.virtual_memory().used
            
            if memory_usage > self.threshold_bytes:
                logger.warning(f"内存使用超过阈值: {memory_usage / 1024 / 1024:.1f} MB")
                
                if callback:
                    callback(memory_usage)
            
            time.sleep(5)  # 每5秒检查一次


class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_dataframe(df: pd.DataFrame, required_columns: List[str] = None,
                          min_rows: int = 1) -> Tuple[bool, List[str]]:
        """验证DataFrame"""
        issues = []
        
        if df is None:
            issues.append("DataFrame为空")
            return False, issues
        
        if len(df) < min_rows:
            issues.append(f"行数不足，需要至少{min_rows}行，实际{len(df)}行")
        
        if required_columns:
            missing_cols = [col for col in required_columns if col not in df.columns]
            if missing_cols:
                issues.append(f"缺少必需的列: {missing_cols}")
        
        # 检查重复行
        if df.duplicated().any():
            dup_count = df.duplicated().sum()
            issues.append(f"发现{dup_count}行重复数据")
        
        # 检查空值
        null_counts = df.isnull().sum()
        high_null_cols = null_counts[null_counts > len(df) * 0.5].index.tolist()
        if high_null_cols:
            issues.append(f"以下列空值比例超过50%: {high_null_cols}")
        
        return len(issues) == 0, issues
    
    @staticmethod
    def validate_numeric_column(series: pd.Series, min_val: float = None,
                               max_val: float = None) -> Tuple[bool, List[str]]:
        """验证数值列"""
        issues = []
        
        # 检查数据类型
        if not pd.api.types.is_numeric_dtype(series):
            issues.append("不是数值类型")
            return False, issues
        
        # 检查范围
        if min_val is not None:
            below_min = (series < min_val).sum()
            if below_min > 0:
                issues.append(f"{below_min}个值低于最小值{min_val}")
        
        if max_val is not None:
            above_max = (series > max_val).sum()
            if above_max > 0:
                issues.append(f"{above_max}个值高于最大值{max_val}")
        
        # 检查异常值
        Q1 = series.quantile(0.25)
        Q3 = series.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outliers = ((series < lower_bound) | (series > upper_bound)).sum()
        if outliers > len(series) * 0.1:  # 异常值超过10%
            issues.append(f"检测到{outliers}个潜在异常值")
        
        return len(issues) == 0, issues


class BioUtils:
    """生物信息学通用工具"""
    
    @staticmethod
    def reverse_complement(sequence: str) -> str:
        """获取DNA序列的反向互补序列"""
        complement = {'A': 'T', 'T': 'A', 'G': 'C', 'C': 'G',
                     'N': 'N', 'a': 't', 't': 'a', 'g': 'c', 'c': 'g', 'n': 'n'}
        
        return ''.join(complement.get(base, base) for base in reversed(sequence))
    
    @staticmethod
    def translate_dna(sequence: str, frame: int = 0) -> str:
        """翻译DNA序列为蛋白质"""
        genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TAA': '*', 'TAG': '*',
            'TGT': 'C', 'TGC': 'C', 'TGA': '*', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
        
        sequence = sequence.upper()[frame:]
        protein = []
        
        for i in range(0, len(sequence) - 2, 3):
            codon = sequence[i:i+3]
            if len(codon) == 3:
                aa = genetic_code.get(codon, 'X')
                protein.append(aa)
                if aa == '*':  # 终止密码子
                    break
        
        return ''.join(protein)
    
    @staticmethod
    def calculate_gc_content(sequence: str) -> float:
        """计算GC含量"""
        sequence = sequence.upper()
        gc_count = sequence.count('G') + sequence.count('C')
        total_count = len(sequence)
        
        return (gc_count / total_count * 100) if total_count > 0 else 0.0
    
    @staticmethod
    def find_orfs(sequence: str, min_length: int = 300) -> List[Dict[str, Any]]:
        """查找开放阅读框"""
        sequence = sequence.upper()
        orfs = []
        
        for frame in range(3):
            start_pos = frame
            while start_pos < len(sequence) - 2:
                # 查找起始密码子
                if sequence[start_pos:start_pos+3] == 'ATG':
                    # 查找终止密码子
                    for stop_pos in range(start_pos + 3, len(sequence) - 2, 3):
                        codon = sequence[stop_pos:stop_pos+3]
                        if codon in ['TAA', 'TAG', 'TGA']:
                            orf_length = stop_pos + 3 - start_pos
                            if orf_length >= min_length:
                                orfs.append({
                                    'start': start_pos,
                                    'end': stop_pos + 3,
                                    'length': orf_length,
                                    'frame': frame,
                                    'sequence': sequence[start_pos:stop_pos+3]
                                })
                            break
                    
                start_pos += 3
        
        return sorted(orfs, key=lambda x: x['length'], reverse=True)


class NetworkUtils:
    """网络工具"""
    
    @staticmethod
    @retry(max_attempts=3, delay=1.0)
    def download_file(url: str, output_path: Path, chunk_size: int = 8192) -> bool:
        """下载文件"""
        try:
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            
            with open(output_path, 'wb') as f:
                if total_size > 0:
                    progress = ProgressBar(total_size, f"下载 {output_path.name}")
                    
                    for chunk in response.iter_content(chunk_size=chunk_size):
                        if chunk:
                            f.write(chunk)
                            progress.update(len(chunk))
                else:
                    for chunk in response.iter_content(chunk_size=chunk_size):
                        if chunk:
                            f.write(chunk)
            
            return True
            
        except Exception as e:
            logger.error(f"下载文件失败 {url}: {e}")
            return False
    
    @staticmethod
    def check_internet_connection(url: str = "https://www.google.com", timeout: int = 5) -> bool:
        """检查网络连接"""
        try:
            response = requests.get(url, timeout=timeout)
            return response.status_code == 200
        except:
            return False


# 全局工具实例
cache = DataCache()
progress_bar = ProgressBar
timer = Timer
file_manager = FileManager()
system_info = SystemInfo()


# 便捷函数
def setup_logging(level: int = logging.INFO, format_string: str = None) -> None:
    """设置日志配置"""
    if format_string is None:
        format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=level,
        format=format_string,
        handlers=[
            logging.StreamHandler(sys.stdout),
        ]
    )


def get_memory_usage() -> float:
    """获取当前内存使用量（MB）"""
    import psutil
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024


def get_cpu_usage() -> float:
    """获取当前CPU使用率"""
    import psutil
    return psutil.cpu_percent(interval=1)


def create_temp_file(suffix: str = '', prefix: str = 'temp_', dir: str = None) -> Path:
    """创建临时文件"""
    fd, path = tempfile.mkstemp(suffix=suffix, prefix=prefix, dir=dir)
    os.close(fd)
    return Path(path)


def create_temp_dir(prefix: str = 'temp_', dir: str = None) -> Path:
    """创建临时目录"""
    return Path(tempfile.mkdtemp(prefix=prefix, dir=dir))


if __name__ == '__main__':
    # 测试工具函数
    print("工具模块测试")
    
    # 测试计时器
    with Timer("测试操作"):
        time.sleep(1)
    
    # 测试系统信息
    info = SystemInfo.get_system_info()
    print(f"系统信息: {info}")
    
    # 测试依赖检查
    deps = SystemInfo.check_dependencies()
    print(f"依赖状态: {deps}")
    
    # 测试进度条
    pbar = ProgressBar(100, "测试进度")
    for i in range(100):
        time.sleep(0.01)
        pbar.update()
    
    print("测试完成")