# 梭菌突变分析工作流程 (Clostridium Mutation Analysis Workflow)

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/your-repo/clostridium-workflow)

一个专门用于分析梭菌（*Clostridium ljungdahlii*）基因组突变对代谢网络和次级代谢产物影响的综合生物信息学工作流程。

## 🌟 主要特性

### 核心功能
- **VCF文件处理**: 解析snpEff注释的VCF文件，提取非同义突变
- **功能注释整合**: 整合eggNOG-mapper、GO、KEGG、Pfam等多种注释数据库
- **代谢模型分析**: 基于COBRA工具包分析突变对代谢网络的影响
- **通路富集分析**: 识别受突变显著影响的生物学通路
- **Wood-Ljungdahl通路特异性分析**: 专门针对梭菌核心代谢通路的分析
- **高质量可视化**: 生成发表级别的图表和交互式报告

### 技术特点
- 🚀 **模块化设计**: 每个分析步骤独立，易于维护和扩展
- 🔬 **科学严谨**: 基于最新的生物信息学方法和算法
- 📊 **丰富可视化**: 支持静态图表、交互式图表和发表级别图表
- ⚡ **高性能**: 支持并行处理和智能缓存
- 🔧 **高度可配置**: 通过YAML配置文件灵活控制所有参数
- 🛡️ **质量保证**: 内置全面的质量控制和数据验证

## 📋 系统要求

### 最低要求
- **操作系统**: Linux, macOS, Windows 10+
- **Python**: 3.8 或更高版本
- **内存**: 8GB RAM（推荐16GB+）
- **存储**: 10GB可用空间（用于数据和缓存）
- **网络**: 稳定的网络连接（用于下载数据库）

### 推荐配置
- **CPU**: 4核心以上
- **内存**: 32GB RAM
- **存储**: SSD存储，50GB可用空间
- **网络**: 高速网络连接

## 🚀 快速开始

### 1. 安装

#### 使用conda（推荐）
```bash
# 克隆仓库
git clone https://github.com/your-repo/clostridium-mutation-workflow.git
cd clostridium-mutation-workflow

# 创建conda环境
conda env create -f environment.yml
conda activate clostridium-workflow

# 验证安装
python main_workflow.py --test
```

#### 使用pip
```bash
# 克隆仓库
git clone https://github.com/your-repo/clostridium-mutation-workflow.git
cd clostridium-mutation-workflow

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 验证安装
python main_workflow.py --test
```

#### 使用Docker
```bash
# 构建镜像
docker build -t clostridium-workflow .

# 运行容器
docker run -v $(pwd)/data:/app/data \
           -v $(pwd)/results:/app/results \
           clostridium-workflow \
           --config config/example_config.yaml
```

### 2. 准备数据

#### 必需文件
1. **snpEff注释的VCF文件**
   ```bash
   # 使用snpEff注释VCF文件
   java -jar snpEff.jar clostridium_ljungdahlii your_mutations.vcf > annotated_mutations.vcf
   ```

2. **eggNOG-mapper功能注释文件**
   ```bash
   # 运行eggNOG-mapper
   emapper.py -i proteins.faa -o clostridium_annotations \
              --tax_scope bacteria --sensmode more-sensitive
   ```

#### 可选文件
- 参考基因组（FASTA格式）
- GFF注释文件
- 代谢模型（SBML格式）

### 3. 配置工作流程

复制示例配置文件并根据您的数据进行修改：
```bash
cp config/example_config.yaml config/my_analysis.yaml
# 编辑配置文件以匹配您的数据路径
```

### 4. 运行分析

#### 完整分析
```bash
python main_workflow.py --config config/my_analysis.yaml
```

#### 分步运行
```bash
# 只运行VCF处理
python vcf_processor.py data/annotated_mutations.vcf --config config/my_analysis.yaml

# 只运行富集分析
python enrichment_analyzer.py --config config/my_analysis.yaml

# 生成可视化
python visualization.py --config config/my_analysis.yaml
```

#### 质量控制
```bash
# 运行质量检查
python quality_control.py --config config/my_analysis.yaml
```

## 📁 项目结构

```
clostridium-mutation-workflow/
├── README.md                          # 项目文档
├── LICENSE                            # 许可证
├── requirements.txt                   # Python依赖
├── environment.yml                    # Conda环境文件
├── Dockerfile                         # Docker配置
├── .gitignore                         # Git忽略文件
│
├── config/                            # 配置文件
│   ├── example_config.yaml           # 示例配置
│   └── default_config.yaml           # 默认配置
│
├── src/                              # 源代码
│   ├── __init__.py
│   ├── config_manager.py             # 配置管理
│   ├── vcf_processor.py              # VCF处理
│   ├── annotation_integrator.py      # 注释整合
│   ├── metabolic_analyzer.py         # 代谢分析
│   ├── enrichment_analyzer.py        # 富集分析
│   ├── main_workflow.py              # 主工作流程
│   ├── visualization.py              # 可视化
│   ├── quality_control.py            # 质量控制
│   └── utils.py                      # 工具函数
│
├── tests/                            # 测试文件
│   ├── test_vcf_processor.py
│   ├── test_enrichment.py
│   └── test_data/
│
├── docs/                             # 详细文档
│   ├── installation.md              # 安装指南
│   ├── user_guide.md                # 用户指南
│   ├── data_formats.md              # 数据格式说明
│   ├── troubleshooting.md           # 故障排除
│   └── api_reference.md             # API参考
│
├── examples/                         # 示例数据
│   ├── sample_data/
│   └── tutorials/
│
└── scripts/                          # 辅助脚本
    ├── download_databases.sh         # 数据库下载
    ├── setup_environment.sh          # 环境设置
    └── validate_installation.py      # 安装验证
```

## 🔧 详细配置

### 配置文件结构

配置文件采用YAML格式，主要包含以下部分：

```yaml
# 项目基本信息
project:
  name: "my_clostridium_analysis"
  organism: "Clostridium ljungdahlii"
  strain: "DSM_13528"

# 输入文件
input:
  vcf_file: "data/mutations.vcf"
  eggnog_annotations: "data/annotations.emapper.annotations"
  # 更多配置...

# VCF过滤参数
vcf_filter:
  min_quality: 30.0
  impact_levels: ["HIGH", "MODERATE"]
  # 更多参数...

# 其他模块配置...
```

### 重要配置项

#### VCF过滤配置
- `min_quality`: 最小质量分数（建议30+）
- `min_read_depth`: 最小读取深度（建议10+）
- `impact_levels`: 影响等级过滤（HIGH, MODERATE, LOW）
- `non_synonymous_effects`: 非同义突变类型列表

#### 代谢模型配置
- `essential_gene_threshold`: 必需基因阈值（默认0.01）
- `target_metabolites`: 目标代谢产物交换反应ID
- `medium_conditions`: 培养基组成设置

#### 富集分析配置
- `significance_threshold`: 显著性阈值（默认0.05）
- `fdr_method`: 多重检验校正方法
- `custom_pathways`: 自定义通路定义

## 📊 输出结果

### 文件结构
```
results_clostridium_analysis/
├── workflow_results.json             # 完整结果JSON
├── analysis_summary.json             # 分析摘要
├── analysis_report.html              # HTML报告
│
├── mutations/                         # 突变分析结果
│   ├── mutations_detailed.tsv        # 详细突变表
│   ├── mutations_detailed.xlsx       # Excel格式
│   └── mutation_summary.json         # 突变摘要
│
├── enrichment_analysis/               # 富集分析结果
│   ├── enrichment_results.csv        # 富集结果
│   ├── significant_enrichments.csv   # 显著富集
│   ├── enrichment_analysis.xlsx      # Excel报告
│   └── enrichment_results.json       # JSON格式
│
├── metabolic_analysis/                # 代谢分析结果
│   ├── metabolic_impacts.csv         # 代谢影响
│   ├── metabolic_impacts.xlsx        # Excel格式
│   ├── baseline_fva.csv              # 基线FVA结果
│   └── metabolic_impacts.json        # JSON格式
│
├── visualizations/                    # 可视化结果
│   ├── mutation_impact_distribution.png
│   ├── top_enriched_pathways.png
│   ├── metabolic_network_plot.png
│   ├── interactive_report.html       # 交互式报告
│   └── publication_figures/          # 发表级别图表
│
├── quality_control/                   # 质量控制
│   ├── quality_report.json           # JSON报告
│   └── quality_report.txt            # 文本报告
│
└── logs/                             # 日志文件
    └── workflow_20250115_143022.log  # 执行日志
```

### 主要输出文件说明

#### 1. 突变分析结果
- **mutations_detailed.tsv**: 包含所有非同义突变的详细信息
- **mutation_summary.json**: 突变统计摘要
- 字段包括：基因ID、突变类型、影响等级、氨基酸变化等

#### 2. 富集分析结果
- **enrichment_results.csv**: 所有通路的富集分析结果
- **significant_enrichments.csv**: 仅包含显著富集的通路
- 字段包括：通路ID、通路名称、富集倍数、P值等

#### 3. 代谢分析结果
- **metabolic_impacts.csv**: 基因突变对代谢的影响
- 字段包括：基因ID、是否必需、生长率变化、受影响通路等

#### 4. 可视化结果
- **静态图表**: PNG/SVG格式的各种分析图表
- **交互式报告**: HTML格式，支持缩放和筛选
- **发表级别图表**: 高分辨率，适合论文发表

## 🧬 生物学解读

### Wood-Ljungdahl通路分析

工作流程特别关注梭菌的核心代谢通路：

1. **东部甲基支路**: CO₂ → 甲酸 → 甲酰-THF → 甲基-THF
2. **西部羰基支路**: CO → 羰基中间体 → 乙酰辅酶A
3. **能量守恒**: Rnf复合体和电子分叉反应

### 关键基因功能

- **fdhA/fdhB**: 甲酸脱氢酶，催化CO₂还原为甲酸
- **acsA/acsB**: 乙酰辅酶A合成酶，催化CO和甲基-THF合成乙酰辅酶A
- **rnfCDGEAB**: Rnf复合体，主要能量守恒机制
- **nfnAB**: Nfn复合体，NADPH生成

### 代谢产物影响

工作流程分析突变对以下产物的影响：
- 乙醇（EX_etoh_e）
- 乙酸（EX_ac_e）
- 丁酸（EX_but_e）
- 氢气（EX_h2_e）

## 📈 高级用法

### 自定义通路分析

添加新的自定义通路：

```yaml
pathway_analysis:
  custom_pathways:
    My_Custom_Pathway:
      - "gene1"
      - "gene2"
      - "gene3"
```

### 并行处理优化

```yaml
computing:
  n_jobs: 8                    # 使用8个CPU核心
  memory_limit: "32G"          # 限制内存使用
  chunk_size: 50000           # 增大分块大小
```

### API配置

```yaml
advanced:
  api_config:
    kegg_api_delay: 0.3        # 加快KEGG查询
    uniprot_batch_size: 200    # 增大批处理大小
```

## 🐛 故障排除

### 常见问题

#### 1. 内存不足
```bash
# 症状：MemoryError或进程被杀死
# 解决：减少并行度或增加交换空间
computing:
  n_jobs: 2                    # 减少并行进程
  chunk_size: 5000            # 减小分块大小
```

#### 2. 网络连接问题
```bash
# 症状：API请求超时
# 解决：增加超时时间或使用缓存
advanced:
  api_config:
    go_api_timeout: 60         # 增加超时时间
computing:
  use_cache: true             # 启用缓存
```

#### 3. 文件格式问题
```bash
# 症状：解析错误
# 解决：检查文件格式和编码
python quality_control.py --check-files-only --config your_config.yaml
```

### 日志分析

启用详细日志：
```bash
python main_workflow.py --config config.yaml --verbose
```

检查日志文件：
```bash
tail -f results/logs/workflow_*.log
```

### 性能优化

#### 1. 使用SSD存储
- 将工作目录和缓存目录设置在SSD上

#### 2. 优化内存使用
```yaml
computing:
  memory_limit: "16G"          # 根据系统内存调整
  chunk_size: 20000           # 平衡内存和性能
```

#### 3. 网络优化
```yaml
advanced:
  api_config:
    kegg_api_delay: 0.2        # 在允许范围内减少延迟
  experimental:
    parallel_database_queries: true  # 启用并行查询
```

## 📚 引用和致谢

### 引用本工具

如果您在研究中使用了本工作流程，请引用：

```
Your Name et al. (2025). Clostridium Mutation Analysis Workflow: 
A comprehensive pipeline for analyzing genomic mutations in 
Clostridium ljungdahlii. GitHub repository: 
https://github.com/your-repo/clostridium-mutation-workflow
```

### 依赖工具致谢

本工作流程基于以下优秀的工具和数据库：

- **snpEff**: 变异注释 [Cingolani et al., 2012]
- **eggNOG-mapper**: 功能注释 [Cantalapiedra et al., 2021]
- **COBRApy**: 代谢模型分析 [Ebrahim et al., 2013]
- **KEGG**: 通路数据库 [Kanehisa & Goto, 2000]
- **Gene Ontology**: 功能分类 [Ashburner et al., 2000]
- **BiGG Models**: 代谢模型数据库 [King et al., 2016]

## 🤝 贡献指南

欢迎贡献代码、报告问题或提出功能请求！

### 报告问题
1. 检查[已知问题](https://github.com/your-repo/issues)
2. 创建新的issue，包含：
   - 详细的错误描述
   - 完整的错误信息
   - 系统信息和Python版本
   - 配置文件（去除敏感信息）

### 贡献代码
1. Fork本仓库
2. 创建功能分支：`git checkout -b feature-name`
3. 提交更改：`git commit -m "Add feature"`
4. 推送分支：`git push origin feature-name`
5. 创建Pull Request

### 开发环境设置
```bash
# 克隆开发版本
git clone https://github.com/your-repo/clostridium-mutation-workflow.git
cd clostridium-mutation-workflow

# 安装开发依赖
pip install -r requirements-dev.txt

# 运行测试
python -m pytest tests/

# 检查代码质量
flake8 src/
black src/
```

## 📄 许可证

本项目采用MIT许可证 - 详见[LICENSE](LICENSE)文件。

## 📞 联系方式

- **项目主页**: https://github.com/your-repo/clostridium-mutation-workflow
- **文档**: https://clostridium-workflow.readthedocs.io
- **问题报告**: https://github.com/your-repo/issues
- **邮箱**: <EMAIL>

## 📝 更新日志

### v3.0.0 (2025-01-15)
- 🎉 初始版本发布
- ✨ 完整的VCF处理管道
- ✨ 代谢模型分析功能
- ✨ 通路富集分析
- ✨ 高质量可视化
- ✨ 全面的质量控制

### 计划功能 (v3.1.0)
- 🔄 支持更多文件格式
- 🚀 GPU加速计算
- 🤖 机器学习预测
- 🌐 Web界面
- 📱 移动端支持

---

*为梭菌代谢工程研究提供强大的生物信息学支持* 🧬🔬