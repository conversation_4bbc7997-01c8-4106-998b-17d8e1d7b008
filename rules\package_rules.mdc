---
description: 
globs: 
alwaysApply: true
---
# 📦 Package Installation Rules

## 🌟 Goal
Ensure that IDE installs the **latest** and **correct** packages using **accurate syntax** by default. Avoid relying on outdated model knowledge.

---

## ✅ General Guidelines

1. **Always use the latest version** of a package unless explicitly specified.
2. **Do not hardcode version numbers** unless required — let the package manager resolve the latest version automatically.
3. **Avoid relying solely on internal AI memory or training data** — search the web (e.g., npmjs.com, GitHub, or official docs) for the most accurate and up-to-date package versions and usage syntax.
4. **When in doubt, check the official documentation or GitHub repository**.

---

## 🔄 Installing Packages – Use Correct Commands

### ✅ Correct Examples

```bash
# pnpm
pnpm add -D @typescript-eslint/eslint-plugin

# yarn
yarn add -D @typescript-eslint/eslint-plugin

# npm
npm install --save-dev @typescript-eslint/eslint-plugin
```

### ❌ Incorrect Example (Don't do this)

```bash
# Hardcoded version (not future-proof)
yarn add -D @typescript-eslint/eslint-plugin@5.0.0
```

---

## 🧠 Smart Suggestions (If Uncertain)

> 🔍 **If the AI is unsure about the latest package name or version**, it should:
- Prompt to **search the web** for the most accurate info.
- Or use the install command **without version numbers**.

---

## ⚠️ Known Issues & Fixes

### ❌ Incorrect CLI Syntax

**Incorrect:**
```bash
npx shadcn-ui@latest add button
```

**✅ Correct:**
```bash
npx shadcn@latest add button
```

> ✅ Always refer to the [official documentation](mdc:https:/ui.shadcn.dev/docs/installation) to confirm command syntax.

---

## ✅ Package Manager Defaults

Use these defaults unless overridden:

| Task              | Yarn                        | pnpm                         | npm                          |
|-------------------|-----------------------------|------------------------------|------------------------------|
| Add dev dependency| `yarn add -D <package>`     | `pnpm add -D <package>`      | `npm install --save-dev <package>` |
| Add regular dep   | `yarn add <package>`        | `pnpm add <package>`         | `npm install <package>`      |

---

## ✅ Final Checklist Before Installing

- [ ] Are you using the correct package name?
- [ ] Are you omitting hardcoded versions unless required?
- [ ] Did you confirm the latest syntax from official sources?
- [ ] If uncertain, are you searching the web or using generic install commands?

---