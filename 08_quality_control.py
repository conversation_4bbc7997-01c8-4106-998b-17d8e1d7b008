#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
质量控制模块 - 梭菌生物信息学工作流程
作者：生物信息学分析团队
版本：3.0.0
描述：对输入数据、中间结果和最终输出进行质量检查和验证
"""

import logging
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
import json
import gzip
from collections import defaultdict, Counter
import re

# 生物信息学库
import cyvcf2
from Bio import SeqIO
import pysam

# 导入项目模块
from config_manager import ConfigManager
from vcf_processor import MutationRecord
from metabolic_analyzer import MetabolicImpact
from enrichment_analyzer import EnrichmentResult

logger = logging.getLogger(__name__)


@dataclass
class QualityReport:
    """质量控制报告数据结构"""
    # 基本信息
    analysis_date: str = ""
    input_files: Dict[str, str] = field(default_factory=dict)
    
    # 文件质量检查
    file_checks: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # VCF质量检查
    vcf_quality: Dict[str, Any] = field(default_factory=dict)
    
    # 注释质量检查
    annotation_quality: Dict[str, Any] = field(default_factory=dict)
    
    # 结果质量检查
    results_quality: Dict[str, Any] = field(default_factory=dict)
    
    # 警告和错误
    warnings: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    
    # 质量评分
    overall_quality_score: float = 0.0
    quality_grade: str = "未评估"
    
    # 建议
    recommendations: List[str] = field(default_factory=list)


class QualityController:
    """质量控制器类"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化质量控制器
        
        参数:
            config: 配置管理器实例
        """
        self.config = config
        self.report = QualityReport()
        
        # 质量阈值
        self.quality_thresholds = {
            'min_vcf_quality': 20.0,
            'min_read_depth': 5,
            'min_annotation_coverage': 0.5,
            'min_pathway_size': 3,
            'max_pathway_size': 500,
            'min_enrichment_pvalue': 0.05,
            'min_sample_mutations': 10
        }
        
        # 从配置中更新阈值
        if hasattr(config, 'quality_thresholds'):
            self.quality_thresholds.update(config.quality_thresholds)
        
        logger.info("质量控制器初始化完成")
    
    def run_comprehensive_quality_check(self, mutations: List[MutationRecord] = None,
                                       metabolic_impacts: List[MetabolicImpact] = None,
                                       enrichment_results: List[EnrichmentResult] = None) -> QualityReport:
        """
        运行全面的质量检查
        
        参数:
            mutations: 突变记录列表（可选）
            metabolic_impacts: 代谢影响列表（可选）
            enrichment_results: 富集分析结果列表（可选）
            
        返回:
            质量控制报告
        """
        logger.info("开始全面质量检查...")
        
        try:
            # 1. 输入文件质量检查
            logger.info("检查输入文件质量...")
            self._check_input_files_quality()
            
            # 2. VCF文件特异性检查
            logger.info("检查VCF文件质量...")
            self._check_vcf_quality()
            
            # 3. 注释文件质量检查
            logger.info("检查注释文件质量...")
            self._check_annotation_quality()
            
            # 4. 代谢模型质量检查
            if self.config.input_files.get('model_file'):
                logger.info("检查代谢模型质量...")
                self._check_metabolic_model_quality()
            
            # 5. 分析结果质量检查
            if mutations:
                logger.info("检查突变分析结果质量...")
                self._check_mutation_results_quality(mutations)
            
            if metabolic_impacts:
                logger.info("检查代谢影响分析质量...")
                self._check_metabolic_results_quality(metabolic_impacts)
            
            if enrichment_results:
                logger.info("检查富集分析结果质量...")
                self._check_enrichment_results_quality(enrichment_results)
            
            # 6. 计算整体质量评分
            self._calculate_overall_quality_score()
            
            # 7. 生成建议
            self._generate_recommendations()
            
            # 8. 保存质量报告
            self._save_quality_report()
            
            logger.info(f"质量检查完成，整体质量评分: {self.report.overall_quality_score:.2f} ({self.report.quality_grade})")
            
            return self.report
            
        except Exception as e:
            error_msg = f"质量检查过程中出错: {e}"
            logger.error(error_msg)
            self.report.errors.append(error_msg)
            return self.report
    
    def _check_input_files_quality(self) -> None:
        """检查输入文件的质量"""
        
        for file_type, file_path in self.config.input_files.items():
            if not file_path:
                continue
            
            file_check = {
                'exists': False,
                'readable': False,
                'size_mb': 0.0,
                'format_valid': False,
                'issues': []
            }
            
            try:
                path = Path(file_path)
                
                # 检查文件是否存在
                if path.exists():
                    file_check['exists'] = True
                    file_check['size_mb'] = path.stat().st_size / (1024 * 1024)
                    
                    # 检查是否可读
                    try:
                        with open(path, 'r') as f:
                            f.read(1024)  # 读取前1KB
                        file_check['readable'] = True
                    except:
                        file_check['issues'].append("文件不可读或为二进制文件")
                    
                    # 文件格式特异性检查
                    if file_type == 'vcf_file':
                        file_check['format_valid'] = self._validate_vcf_format(path)
                    elif file_type == 'eggnog_annotations':
                        file_check['format_valid'] = self._validate_annotation_format(path)
                    elif file_type == 'gff_file':
                        file_check['format_valid'] = self._validate_gff_format(path)
                    elif file_type == 'model_file':
                        file_check['format_valid'] = self._validate_model_format(path)
                    else:
                        file_check['format_valid'] = True  # 其他文件类型暂不检查
                
                else:
                    file_check['issues'].append("文件不存在")
                
                # 大小检查
                if file_check['size_mb'] == 0:
                    file_check['issues'].append("文件为空")
                elif file_check['size_mb'] > 1000:  # 超过1GB
                    file_check['issues'].append("文件过大，可能影响处理速度")
                
            except Exception as e:
                file_check['issues'].append(f"检查文件时出错: {e}")
            
            self.report.file_checks[file_type] = file_check
    
    def _validate_vcf_format(self, vcf_path: Path) -> bool:
        """验证VCF文件格式"""
        try:
            # 检查文件头
            with open(vcf_path, 'r') as f:
                first_line = f.readline().strip()
                if not first_line.startswith('##fileformat=VCF'):
                    return False
                
                # 检查是否有snpEff注释
                has_snpeff = False
                for _ in range(100):  # 检查前100行
                    line = f.readline()
                    if not line:
                        break
                    if '##SnpEffVersion' in line or '##INFO=<ID=ANN' in line:
                        has_snpeff = True
                        break
                
                if not has_snpeff:
                    self.report.warnings.append("VCF文件可能缺少snpEff注释")
            
            # 使用cyvcf2进一步验证
            vcf = cyvcf2.VCF(str(vcf_path))
            sample_count = len(vcf.samples)
            vcf.close()
            
            if sample_count == 0:
                self.report.warnings.append("VCF文件中没有样本信息")
            
            return True
            
        except Exception as e:
            self.report.errors.append(f"VCF格式验证失败: {e}")
            return False
    
    def _validate_annotation_format(self, annotation_path: Path) -> bool:
        """验证注释文件格式"""
        try:
            # 检查eggNOG-mapper输出格式
            df = pd.read_csv(annotation_path, sep='\t', comment='#', nrows=5)
            
            # 检查必需的列
            required_columns = ['#query', 'eggNOG_OGs', 'Description']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                self.report.errors.append(f"注释文件缺少必需的列: {missing_columns}")
                return False
            
            return True
            
        except Exception as e:
            self.report.errors.append(f"注释格式验证失败: {e}")
            return False
    
    def _validate_gff_format(self, gff_path: Path) -> bool:
        """验证GFF文件格式"""
        try:
            with open(gff_path, 'r') as f:
                for line_num, line in enumerate(f):
                    if line_num > 100:  # 只检查前100行
                        break
                    
                    if line.startswith('#'):
                        continue
                    
                    fields = line.strip().split('\t')
                    if len(fields) != 9:
                        self.report.warnings.append(f"GFF文件第{line_num+1}行格式不正确")
                        continue
                    
                    # 检查第3列是否为有效的特征类型
                    feature_type = fields[2]
                    if feature_type not in ['gene', 'CDS', 'exon', 'transcript', 'mRNA']:
                        if line_num == 1:  # 只对第一个非注释行发出警告
                            self.report.warnings.append("GFF文件可能包含非标准特征类型")
                    
                    break  # 只检查第一个数据行
            
            return True
            
        except Exception as e:
            self.report.errors.append(f"GFF格式验证失败: {e}")
            return False
    
    def _validate_model_format(self, model_path: Path) -> bool:
        """验证代谢模型文件格式"""
        try:
            if model_path.suffix.lower() == '.xml':
                # 检查SBML格式
                with open(model_path, 'r') as f:
                    content = f.read(1000)  # 读取前1000个字符
                    if '<?xml' not in content or 'sbml' not in content.lower():
                        self.report.errors.append("模型文件不是有效的SBML格式")
                        return False
            
            elif model_path.suffix.lower() == '.json':
                # 检查JSON格式
                with open(model_path, 'r') as f:
                    json.load(f)
            
            else:
                self.report.warnings.append("未知的模型文件格式")
            
            return True
            
        except Exception as e:
            self.report.errors.append(f"模型格式验证失败: {e}")
            return False
    
    def _check_vcf_quality(self) -> None:
        """检查VCF文件质量"""
        vcf_file = self.config.input_files.get('vcf_file')
        if not vcf_file or not Path(vcf_file).exists():
            return
        
        vcf_stats = {
            'total_variants': 0,
            'snp_count': 0,
            'indel_count': 0,
            'quality_distribution': [],
            'depth_distribution': [],
            'low_quality_variants': 0,
            'low_depth_variants': 0,
            'missing_annotations': 0,
            'samples_count': 0,
            'issues': []
        }
        
        try:
            vcf = cyvcf2.VCF(vcf_file)
            vcf_stats['samples_count'] = len(vcf.samples)
            
            # 统计前1000个变异的质量信息
            for i, variant in enumerate(vcf):
                if i >= 1000:  # 限制检查数量以提高速度
                    break
                
                vcf_stats['total_variants'] += 1
                
                # 变异类型统计
                if len(variant.REF) == 1 and all(len(alt) == 1 for alt in variant.ALT):
                    vcf_stats['snp_count'] += 1
                else:
                    vcf_stats['indel_count'] += 1
                
                # 质量统计
                if variant.QUAL is not None:
                    vcf_stats['quality_distribution'].append(variant.QUAL)
                    if variant.QUAL < self.quality_thresholds['min_vcf_quality']:
                        vcf_stats['low_quality_variants'] += 1
                
                # 深度统计
                if hasattr(variant, 'gt_depths') and variant.gt_depths:
                    depths = [d for d in variant.gt_depths if d is not None]
                    if depths:
                        avg_depth = np.mean(depths)
                        vcf_stats['depth_distribution'].append(avg_depth)
                        if avg_depth < self.quality_thresholds['min_read_depth']:
                            vcf_stats['low_depth_variants'] += 1
                
                # 注释检查
                if 'ANN' not in variant.INFO:
                    vcf_stats['missing_annotations'] += 1
            
            vcf.close()
            
            # 质量评估
            if vcf_stats['total_variants'] == 0:
                vcf_stats['issues'].append("VCF文件中没有变异")
            
            if vcf_stats['samples_count'] == 0:
                vcf_stats['issues'].append("VCF文件中没有样本")
            
            if vcf_stats['low_quality_variants'] > vcf_stats['total_variants'] * 0.5:
                vcf_stats['issues'].append("超过50%的变异质量分数过低")
            
            if vcf_stats['low_depth_variants'] > vcf_stats['total_variants'] * 0.3:
                vcf_stats['issues'].append("超过30%的变异读取深度过低")
            
            if vcf_stats['missing_annotations'] > vcf_stats['total_variants'] * 0.1:
                vcf_stats['issues'].append("超过10%的变异缺少注释信息")
            
            # 计算统计值
            if vcf_stats['quality_distribution']:
                vcf_stats['mean_quality'] = np.mean(vcf_stats['quality_distribution'])
                vcf_stats['median_quality'] = np.median(vcf_stats['quality_distribution'])
            
            if vcf_stats['depth_distribution']:
                vcf_stats['mean_depth'] = np.mean(vcf_stats['depth_distribution'])
                vcf_stats['median_depth'] = np.median(vcf_stats['depth_distribution'])
            
        except Exception as e:
            vcf_stats['issues'].append(f"VCF质量检查时出错: {e}")
        
        self.report.vcf_quality = vcf_stats
    
    def _check_annotation_quality(self) -> None:
        """检查注释文件质量"""
        annotation_file = self.config.input_files.get('eggnog_annotations')
        if not annotation_file or not Path(annotation_file).exists():
            return
        
        annotation_stats = {
            'total_genes': 0,
            'annotated_genes': 0,
            'coverage_stats': {},
            'quality_distribution': [],
            'issues': []
        }
        
        try:
            df = pd.read_csv(annotation_file, sep='\t', comment='#')
            annotation_stats['total_genes'] = len(df)
            
            # 计算各类注释的覆盖率
            annotation_fields = {
                'Description': 'functional_description',
                'GOs': 'go_terms', 
                'KEGG_ko': 'kegg_annotations',
                'PFAMs': 'pfam_domains',
                'EC': 'ec_numbers'
            }
            
            for field, field_name in annotation_fields.items():
                if field in df.columns:
                    non_empty = df[field].notna() & (df[field] != '-') & (df[field] != '')
                    coverage = non_empty.sum() / len(df)
                    annotation_stats['coverage_stats'][field_name] = coverage
                    
                    if coverage < self.quality_thresholds['min_annotation_coverage']:
                        annotation_stats['issues'].append(f"{field_name}覆盖率过低: {coverage:.2%}")
            
            # 检查E-value分布
            if 'evalue' in df.columns:
                evalues = pd.to_numeric(df['evalue'], errors='coerce')
                evalues = evalues.dropna()
                if len(evalues) > 0:
                    annotation_stats['mean_evalue'] = evalues.mean()
                    annotation_stats['median_evalue'] = evalues.median()
                    
                    # 检查是否有太多高E-value的注释
                    high_evalue_count = (evalues > 0.001).sum()
                    if high_evalue_count > len(evalues) * 0.3:
                        annotation_stats['issues'].append("超过30%的注释E-value过高（>0.001）")
            
            # 检查注释质量
            if 'score' in df.columns:
                scores = pd.to_numeric(df['score'], errors='coerce')
                scores = scores.dropna()
                if len(scores) > 0:
                    annotation_stats['mean_score'] = scores.mean()
                    annotation_stats['median_score'] = scores.median()
            
            # 整体质量评估
            overall_coverage = np.mean(list(annotation_stats['coverage_stats'].values()))
            if overall_coverage < 0.7:
                annotation_stats['issues'].append("整体注释覆盖率较低")
            
        except Exception as e:
            annotation_stats['issues'].append(f"注释质量检查时出错: {e}")
        
        self.report.annotation_quality = annotation_stats
    
    def _check_metabolic_model_quality(self) -> None:
        """检查代谢模型质量"""
        model_file = self.config.input_files.get('model_file')
        if not model_file or not Path(model_file).exists():
            return
        
        # 这里可以添加具体的代谢模型质量检查
        # 比如检查反应平衡、基因-反应映射等
        pass
    
    def _check_mutation_results_quality(self, mutations: List[MutationRecord]) -> None:
        """检查突变分析结果质量"""
        mutation_stats = {
            'total_mutations': len(mutations),
            'unique_genes': len(set(mut.gene_id for mut in mutations)),
            'impact_distribution': {},
            'quality_issues': []
        }
        
        # 影响等级分布
        impact_counts = Counter(mut.impact_level for mut in mutations)
        for impact, count in impact_counts.items():
            mutation_stats['impact_distribution'][impact] = count
        
        # 质量检查
        if len(mutations) < self.quality_thresholds['min_sample_mutations']:
            mutation_stats['quality_issues'].append(
                f"突变数量过少（{len(mutations)}），可能影响分析可靠性"
            )
        
        # 检查是否有极端的突变分布
        if impact_counts.get('HIGH', 0) > len(mutations) * 0.8:
            mutation_stats['quality_issues'].append("高影响突变比例异常高，请检查过滤条件")
        
        if impact_counts.get('LOW', 0) > len(mutations) * 0.9:
            mutation_stats['quality_issues'].append("低影响突变比例过高，分析结果可能不够敏感")
        
        # 基因分布检查
        gene_mutation_counts = Counter(mut.gene_id for mut in mutations)
        max_mutations_per_gene = max(gene_mutation_counts.values())
        if max_mutations_per_gene > 20:
            mutation_stats['quality_issues'].append(
                f"某些基因突变数量异常高（最高{max_mutations_per_gene}个），可能存在测序偏差"
            )
        
        self.report.results_quality['mutation_analysis'] = mutation_stats
    
    def _check_metabolic_results_quality(self, metabolic_impacts: List[MetabolicImpact]) -> None:
        """检查代谢影响分析质量"""
        metabolic_stats = {
            'analyzed_genes': len(metabolic_impacts),
            'essential_genes': len([imp for imp in metabolic_impacts if imp.is_essential]),
            'quality_issues': []
        }
        
        # 检查必需基因比例
        essential_ratio = metabolic_stats['essential_genes'] / len(metabolic_impacts) if metabolic_impacts else 0
        if essential_ratio > 0.5:
            metabolic_stats['quality_issues'].append(
                f"必需基因比例过高（{essential_ratio:.1%}），模型或阈值可能需要调整"
            )
        elif essential_ratio < 0.05:
            metabolic_stats['quality_issues'].append(
                f"必需基因比例过低（{essential_ratio:.1%}），阈值可能过于严格"
            )
        
        # 检查生长率变化分布
        growth_changes = [imp.growth_rate_change for imp in metabolic_impacts 
                         if abs(imp.growth_rate_change) > 0]
        
        if growth_changes:
            extreme_changes = len([gc for gc in growth_changes if abs(gc) > 0.8])
            if extreme_changes > len(growth_changes) * 0.3:
                metabolic_stats['quality_issues'].append("过多基因显示极端的生长率变化")
        
        self.report.results_quality['metabolic_analysis'] = metabolic_stats
    
    def _check_enrichment_results_quality(self, enrichment_results: List[EnrichmentResult]) -> None:
        """检查富集分析结果质量"""
        enrichment_stats = {
            'total_pathways': len(enrichment_results),
            'significant_pathways': len([r for r in enrichment_results if r.is_significant]),
            'database_distribution': {},
            'quality_issues': []
        }
        
        # 数据库分布
        db_counts = Counter(r.database for r in enrichment_results)
        enrichment_stats['database_distribution'] = dict(db_counts)
        
        # 质量检查
        significant_ratio = enrichment_stats['significant_pathways'] / len(enrichment_results) if enrichment_results else 0
        
        if significant_ratio > 0.3:
            enrichment_stats['quality_issues'].append(
                f"显著富集通路比例过高（{significant_ratio:.1%}），可能存在多重检验校正问题"
            )
        elif significant_ratio < 0.01:
            enrichment_stats['quality_issues'].append(
                f"显著富集通路比例过低（{significant_ratio:.1%}），阈值可能过于严格"
            )
        
        # 检查富集倍数分布
        if enrichment_results:
            fold_enrichments = [r.fold_enrichment for r in enrichment_results if r.is_significant]
            if fold_enrichments:
                extreme_enrichments = len([fe for fe in fold_enrichments if fe > 100])
                if extreme_enrichments > 0:
                    enrichment_stats['quality_issues'].append(
                        f"发现{extreme_enrichments}个极端富集倍数（>100），请检查基因集大小"
                    )
        
        self.report.results_quality['enrichment_analysis'] = enrichment_stats
    
    def _calculate_overall_quality_score(self) -> None:
        """计算整体质量评分"""
        scores = []
        
        # 文件质量评分 (25%)
        file_score = self._calculate_file_quality_score()
        scores.append(('file_quality', file_score, 0.25))
        
        # VCF质量评分 (25%)
        vcf_score = self._calculate_vcf_quality_score()
        scores.append(('vcf_quality', vcf_score, 0.25))
        
        # 注释质量评分 (25%)
        annotation_score = self._calculate_annotation_quality_score()
        scores.append(('annotation_quality', annotation_score, 0.25))
        
        # 结果质量评分 (25%)
        results_score = self._calculate_results_quality_score()
        scores.append(('results_quality', results_score, 0.25))
        
        # 计算加权平均分
        weighted_score = sum(score * weight for _, score, weight in scores)
        self.report.overall_quality_score = max(0.0, min(100.0, weighted_score))
        
        # 确定质量等级
        if self.report.overall_quality_score >= 90:
            self.report.quality_grade = "优秀"
        elif self.report.overall_quality_score >= 80:
            self.report.quality_grade = "良好"
        elif self.report.overall_quality_score >= 70:
            self.report.quality_grade = "中等"
        elif self.report.overall_quality_score >= 60:
            self.report.quality_grade = "及格"
        else:
            self.report.quality_grade = "不及格"
    
    def _calculate_file_quality_score(self) -> float:
        """计算文件质量评分"""
        if not self.report.file_checks:
            return 50.0
        
        total_score = 0.0
        for file_type, check in self.report.file_checks.items():
            file_score = 0.0
            
            if check['exists']:
                file_score += 40
            if check['readable']:
                file_score += 30
            if check['format_valid']:
                file_score += 30
            
            # 扣分项
            if check['issues']:
                file_score -= len(check['issues']) * 10
            
            total_score += max(0, file_score)
        
        return total_score / len(self.report.file_checks)
    
    def _calculate_vcf_quality_score(self) -> float:
        """计算VCF质量评分"""
        if not self.report.vcf_quality:
            return 50.0
        
        vcf_stats = self.report.vcf_quality
        score = 100.0
        
        # 扣分项
        if vcf_stats.get('total_variants', 0) == 0:
            score -= 50
        
        if vcf_stats.get('low_quality_variants', 0) > vcf_stats.get('total_variants', 1) * 0.3:
            score -= 20
        
        if vcf_stats.get('low_depth_variants', 0) > vcf_stats.get('total_variants', 1) * 0.3:
            score -= 20
        
        if vcf_stats.get('missing_annotations', 0) > vcf_stats.get('total_variants', 1) * 0.1:
            score -= 10
        
        score -= len(vcf_stats.get('issues', [])) * 10
        
        return max(0, score)
    
    def _calculate_annotation_quality_score(self) -> float:
        """计算注释质量评分"""
        if not self.report.annotation_quality:
            return 50.0
        
        annotation_stats = self.report.annotation_quality
        score = 100.0
        
        # 基于覆盖率评分
        coverage_stats = annotation_stats.get('coverage_stats', {})
        if coverage_stats:
            avg_coverage = np.mean(list(coverage_stats.values()))
            if avg_coverage < 0.5:
                score -= 30
            elif avg_coverage < 0.7:
                score -= 15
        
        # 扣分项
        score -= len(annotation_stats.get('issues', [])) * 10
        
        return max(0, score)
    
    def _calculate_results_quality_score(self) -> float:
        """计算结果质量评分"""
        if not self.report.results_quality:
            return 50.0
        
        score = 100.0
        
        # 检查各分析结果的质量问题
        for analysis_type, stats in self.report.results_quality.items():
            issues = stats.get('quality_issues', [])
            score -= len(issues) * 5  # 每个问题扣5分
        
        return max(0, score)
    
    def _generate_recommendations(self) -> None:
        """生成改进建议"""
        recommendations = []
        
        # 基于质量评分生成建议
        if self.report.overall_quality_score < 70:
            recommendations.append("整体数据质量较低，建议检查输入数据和分析参数")
        
        # 基于具体问题生成建议
        if self.report.vcf_quality.get('low_quality_variants', 0) > 0:
            recommendations.append("考虑提高VCF质量过滤阈值")
        
        if self.report.annotation_quality.get('coverage_stats', {}):
            low_coverage_fields = [
                field for field, coverage in self.report.annotation_quality['coverage_stats'].items()
                if coverage < 0.5
            ]
            if low_coverage_fields:
                recommendations.append(f"以下注释类型覆盖率较低，考虑使用更全面的注释数据库: {', '.join(low_coverage_fields)}")
        
        # 基于结果质量生成建议
        for analysis_type, stats in self.report.results_quality.items():
            if stats.get('quality_issues'):
                recommendations.extend([
                    f"{analysis_type}: {issue}" for issue in stats['quality_issues']
                ])
        
        # 通用建议
        if len(self.report.errors) > 0:
            recommendations.append("存在错误需要修复，建议检查日志文件")
        
        if len(self.report.warnings) > 5:
            recommendations.append("警告数量较多，建议仔细检查数据质量")
        
        self.report.recommendations = recommendations
    
    def _save_quality_report(self) -> None:
        """保存质量控制报告"""
        try:
            # 保存JSON格式报告
            output_dir = Path(self.config.output.results_dir) / 'quality_control'
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 转换为字典格式
            report_dict = {
                'analysis_date': self.report.analysis_date,
                'input_files': self.report.input_files,
                'file_checks': self.report.file_checks,
                'vcf_quality': self.report.vcf_quality,
                'annotation_quality': self.report.annotation_quality,
                'results_quality': self.report.results_quality,
                'warnings': self.report.warnings,
                'errors': self.report.errors,
                'overall_quality_score': self.report.overall_quality_score,
                'quality_grade': self.report.quality_grade,
                'recommendations': self.report.recommendations
            }
            
            # 保存JSON报告
            json_file = output_dir / 'quality_report.json'
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(report_dict, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"质量控制报告已保存到: {json_file}")
            
            # 生成可读的文本报告
            self._generate_text_report(output_dir)
            
        except Exception as e:
            logger.error(f"保存质量报告时出错: {e}")
    
    def _generate_text_report(self, output_dir: Path) -> None:
        """生成可读的文本报告"""
        report_file = output_dir / 'quality_report.txt'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("梭菌突变分析质量控制报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 整体评分
            f.write(f"整体质量评分: {self.report.overall_quality_score:.1f}/100 ({self.report.quality_grade})\n\n")
            
            # 文件质量检查
            f.write("文件质量检查:\n")
            f.write("-" * 20 + "\n")
            for file_type, check in self.report.file_checks.items():
                f.write(f"{file_type}:\n")
                f.write(f"  存在: {'是' if check['exists'] else '否'}\n")
                f.write(f"  可读: {'是' if check['readable'] else '否'}\n")
                f.write(f"  格式有效: {'是' if check['format_valid'] else '否'}\n")
                f.write(f"  大小: {check['size_mb']:.1f} MB\n")
                if check['issues']:
                    f.write(f"  问题: {'; '.join(check['issues'])}\n")
                f.write("\n")
            
            # VCF质量
            if self.report.vcf_quality:
                f.write("VCF文件质量:\n")
                f.write("-" * 20 + "\n")
                vcf = self.report.vcf_quality
                f.write(f"总变异数: {vcf.get('total_variants', 0)}\n")
                f.write(f"SNP: {vcf.get('snp_count', 0)}, INDEL: {vcf.get('indel_count', 0)}\n")
                f.write(f"低质量变异: {vcf.get('low_quality_variants', 0)}\n")
                f.write(f"低深度变异: {vcf.get('low_depth_variants', 0)}\n")
                if vcf.get('mean_quality'):
                    f.write(f"平均质量分数: {vcf['mean_quality']:.1f}\n")
                if vcf.get('issues'):
                    f.write(f"问题: {'; '.join(vcf['issues'])}\n")
                f.write("\n")
            
            # 注释质量
            if self.report.annotation_quality:
                f.write("注释质量:\n")
                f.write("-" * 20 + "\n")
                ann = self.report.annotation_quality
                f.write(f"总基因数: {ann.get('total_genes', 0)}\n")
                coverage = ann.get('coverage_stats', {})
                for field, cov in coverage.items():
                    f.write(f"{field}覆盖率: {cov:.1%}\n")
                if ann.get('issues'):
                    f.write(f"问题: {'; '.join(ann['issues'])}\n")
                f.write("\n")
            
            # 分析结果质量
            if self.report.results_quality:
                f.write("分析结果质量:\n")
                f.write("-" * 20 + "\n")
                for analysis, stats in self.report.results_quality.items():
                    f.write(f"{analysis}:\n")
                    for key, value in stats.items():
                        if key != 'quality_issues':
                            f.write(f"  {key}: {value}\n")
                    if stats.get('quality_issues'):
                        f.write(f"  问题: {'; '.join(stats['quality_issues'])}\n")
                f.write("\n")
            
            # 警告和错误
            if self.report.warnings:
                f.write("警告:\n")
                for warning in self.report.warnings:
                    f.write(f"- {warning}\n")
                f.write("\n")
            
            if self.report.errors:
                f.write("错误:\n")
                for error in self.report.errors:
                    f.write(f"- {error}\n")
                f.write("\n")
            
            # 建议
            if self.report.recommendations:
                f.write("改进建议:\n")
                f.write("-" * 20 + "\n")
                for rec in self.report.recommendations:
                    f.write(f"- {rec}\n")
        
        logger.info(f"文本质量报告已保存到: {report_file}")


# 便捷函数
def run_quality_control(config: ConfigManager, 
                       mutations: List[MutationRecord] = None,
                       metabolic_impacts: List[MetabolicImpact] = None,
                       enrichment_results: List[EnrichmentResult] = None) -> QualityReport:
    """
    运行质量控制的便捷函数
    
    参数:
        config: 配置管理器
        mutations: 突变记录列表（可选）
        metabolic_impacts: 代谢影响列表（可选）
        enrichment_results: 富集分析结果列表（可选）
        
    返回:
        质量控制报告
    """
    qc = QualityController(config)
    return qc.run_comprehensive_quality_check(mutations, metabolic_impacts, enrichment_results)


if __name__ == '__main__':
    # 测试代码
    import argparse
    
    parser = argparse.ArgumentParser(description='质量控制模块测试')
    parser.add_argument('--config', default='config/default_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--check-files-only', action='store_true',
                       help='只检查输入文件质量')
    
    args = parser.parse_args()
    
    # 加载配置
    from config_manager import ConfigManager
    config = ConfigManager(args.config)
    
    # 运行质量控制
    qc = QualityController(config)
    
    if args.check_files_only:
        qc._check_input_files_quality()
        qc._check_vcf_quality()
        qc._check_annotation_quality()
        qc._calculate_overall_quality_score()
        qc._save_quality_report()
    else:
        report = qc.run_comprehensive_quality_check()
    
    print(f"质量检查完成，评分: {qc.report.overall_quality_score:.1f} ({qc.report.quality_grade})")