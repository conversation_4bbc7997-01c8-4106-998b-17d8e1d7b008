#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果可视化模块 - 梭菌生物信息学工作流程
作者：生物信息学分析团队
版本：3.0.0
描述：生成突变分析结果的各种图表和可视化，包括突变分布、通路富集、代谢网络等
"""

import logging
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import json

# 绘图库
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import seaborn as sns
from matplotlib.backends.backend_pdf import PdfPages
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.offline as pyo

# 网络可视化（如果需要）
try:
    import networkx as nx
    import igraph as ig
    NETWORK_AVAILABLE = True
except ImportError:
    NETWORK_AVAILABLE = False
    logging.warning("网络分析库未安装，将跳过网络可视化功能")

# 导入其他模块
from config_manager import ConfigManager
from vcf_processor import MutationRecord
from metabolic_analyzer import MetabolicImpact
from enrichment_analyzer import EnrichmentResult

logger = logging.getLogger(__name__)

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 设置绘图风格
sns.set_style("whitegrid")
plt.style.use('seaborn-v0_8')


class VisualizationGenerator:
    """可视化生成器类"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化可视化生成器
        
        参数:
            config: 配置管理器实例
        """
        self.config = config
        self.output_dir = Path(config.output.results_dir) / 'visualizations'
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 可视化配置
        self.viz_config = config.output.visualization_config
        self.color_scheme = self.viz_config.get('color_scheme', 'viridis')
        self.figure_size = self.viz_config.get('figure_size', (10, 8))
        self.font_size = self.viz_config.get('font_size', 12)
        self.show_statistics = self.viz_config.get('show_statistics', True)
        
        # 输出格式
        self.plot_formats = config.output.plot_formats
        self.plot_dpi = config.output.plot_dpi
        
        # 颜色调色板
        self.colors = {
            'high_impact': '#d62728',      # 红色 - 高影响
            'moderate_impact': '#ff7f0e',  # 橙色 - 中等影响
            'low_impact': '#2ca02c',       # 绿色 - 低影响
            'essential': '#8c564b',        # 棕色 - 必需基因
            'enriched': '#1f77b4',         # 蓝色 - 富集
            'depleted': '#e377c2',         # 粉色 - 贫化
            'wlp_pathway': '#9467bd'       # 紫色 - WLP通路
        }
        
        logger.info(f"可视化生成器初始化完成，输出目录: {self.output_dir}")
    
    def generate_all_visualizations(self, mutations: List[MutationRecord],
                                  metabolic_impacts: List[MetabolicImpact],
                                  enrichment_results: List[EnrichmentResult],
                                  annotation_data: Optional[Dict] = None) -> Dict[str, List[str]]:
        """
        生成所有可视化图表
        
        参数:
            mutations: 突变记录列表
            metabolic_impacts: 代谢影响列表
            enrichment_results: 富集分析结果列表
            annotation_data: 注释数据（可选）
            
        返回:
            生成的图表文件路径字典
        """
        logger.info("开始生成所有可视化图表...")
        
        generated_plots = {
            'mutation_plots': [],
            'metabolic_plots': [],
            'enrichment_plots': [],
            'network_plots': [],
            'summary_plots': []
        }
        
        try:
            # 1. 突变相关可视化
            logger.info("生成突变分析图表...")
            mutation_plots = self.generate_mutation_visualizations(mutations)
            generated_plots['mutation_plots'].extend(mutation_plots)
            
            # 2. 代谢影响可视化
            if metabolic_impacts:
                logger.info("生成代谢影响图表...")
                metabolic_plots = self.generate_metabolic_visualizations(metabolic_impacts)
                generated_plots['metabolic_plots'].extend(metabolic_plots)
            
            # 3. 富集分析可视化
            if enrichment_results:
                logger.info("生成富集分析图表...")
                enrichment_plots = self.generate_enrichment_visualizations(enrichment_results)
                generated_plots['enrichment_plots'].extend(enrichment_plots)
            
            # 4. 网络可视化（如果数据可用）
            if NETWORK_AVAILABLE and annotation_data:
                logger.info("生成网络分析图表...")
                network_plots = self.generate_network_visualizations(
                    mutations, enrichment_results, annotation_data
                )
                generated_plots['network_plots'].extend(network_plots)
            
            # 5. 综合摘要可视化
            logger.info("生成综合摘要图表...")
            summary_plots = self.generate_summary_visualizations(
                mutations, metabolic_impacts, enrichment_results
            )
            generated_plots['summary_plots'].extend(summary_plots)
            
            # 6. 生成交互式HTML报告
            if self.config.output.generate_html_report:
                logger.info("生成交互式HTML可视化...")
                html_file = self.generate_interactive_html_report(
                    mutations, metabolic_impacts, enrichment_results
                )
                generated_plots['summary_plots'].append(html_file)
            
            # 统计生成的图表数量
            total_plots = sum(len(plots) for plots in generated_plots.values())
            logger.info(f"可视化生成完成！总共生成了 {total_plots} 个图表")
            
            return generated_plots
            
        except Exception as e:
            logger.error(f"生成可视化时出错: {e}")
            raise
    
    def generate_mutation_visualizations(self, mutations: List[MutationRecord]) -> List[str]:
        """生成突变相关的可视化"""
        if not mutations:
            logger.warning("没有突变数据，跳过突变可视化")
            return []
        
        generated_files = []
        
        # 1. 突变影响等级分布饼图
        impact_plot = self._plot_mutation_impact_distribution(mutations)
        generated_files.append(impact_plot)
        
        # 2. 变异类型分布条形图
        variant_plot = self._plot_variant_type_distribution(mutations)
        generated_files.append(variant_plot)
        
        # 3. 基因突变频次分布
        gene_freq_plot = self._plot_gene_mutation_frequency(mutations)
        generated_files.append(gene_freq_plot)
        
        # 4. 突变质量分布直方图
        quality_plot = self._plot_mutation_quality_distribution(mutations)
        generated_files.append(quality_plot)
        
        # 5. 氨基酸变化热图
        aa_heatmap = self._plot_amino_acid_change_heatmap(mutations)
        if aa_heatmap:
            generated_files.append(aa_heatmap)
        
        # 6. 染色体/contig分布
        chrom_plot = self._plot_chromosomal_distribution(mutations)
        generated_files.append(chrom_plot)
        
        return generated_files
    
    def _plot_mutation_impact_distribution(self, mutations: List[MutationRecord]) -> str:
        """绘制突变影响等级分布饼图"""
        # 统计影响等级
        impact_counts = pd.Series([mut.impact_level for mut in mutations]).value_counts()
        
        # 创建图表
        fig, ax = plt.subplots(figsize=self.figure_size)
        
        # 定义颜色
        colors = [self.colors.get(f'{level.lower()}_impact', '#888888') for level in impact_counts.index]
        
        # 绘制饼图
        wedges, texts, autotexts = ax.pie(
            impact_counts.values, 
            labels=impact_counts.index,
            colors=colors,
            autopct='%1.1f%%',
            startangle=90,
            textprops={'fontsize': self.font_size}
        )
        
        # 美化文本
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
        
        ax.set_title('突变影响等级分布', fontsize=self.font_size + 2, fontweight='bold', pad=20)
        
        # 添加图例
        ax.legend(wedges, [f'{level} ({count})' for level, count in impact_counts.items()],
                 title="影响等级", loc="center left", bbox_to_anchor=(1, 0, 0.5, 1))
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self._save_plot(fig, 'mutation_impact_distribution')
        plt.close()
        
        return output_file
    
    def _plot_variant_type_distribution(self, mutations: List[MutationRecord]) -> str:
        """绘制变异类型分布条形图"""
        # 统计变异类型
        variant_counts = pd.Series([mut.variant_type for mut in mutations]).value_counts()
        
        # 创建图表
        fig, ax = plt.subplots(figsize=self.figure_size)
        
        # 绘制条形图
        bars = ax.bar(variant_counts.index, variant_counts.values, 
                     color=sns.color_palette(self.color_scheme, len(variant_counts)))
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{int(height)}', ha='center', va='bottom', 
                   fontsize=self.font_size, fontweight='bold')
        
        ax.set_title('突变类型分布', fontsize=self.font_size + 2, fontweight='bold')
        ax.set_xlabel('变异类型', fontsize=self.font_size)
        ax.set_ylabel('突变数量', fontsize=self.font_size)
        ax.tick_params(axis='both', labelsize=self.font_size - 1)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self._save_plot(fig, 'variant_type_distribution')
        plt.close()
        
        return output_file
    
    def _plot_gene_mutation_frequency(self, mutations: List[MutationRecord], top_n: int = 20) -> str:
        """绘制基因突变频次分布（前N个基因）"""
        # 统计每个基因的突变次数
        gene_counts = pd.Series([mut.gene_id for mut in mutations]).value_counts().head(top_n)
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 绘制水平条形图
        bars = ax.barh(range(len(gene_counts)), gene_counts.values,
                      color=sns.color_palette(self.color_scheme, len(gene_counts)))
        
        # 设置y轴标签
        ax.set_yticks(range(len(gene_counts)))
        ax.set_yticklabels([f"{gene}\n({gene_counts[gene]})" for gene in gene_counts.index], 
                          fontsize=self.font_size - 1)
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax.text(width + 0.1, bar.get_y() + bar.get_height()/2,
                   f'{int(width)}', ha='left', va='center', 
                   fontsize=self.font_size - 1, fontweight='bold')
        
        ax.set_title(f'突变频次最高的{top_n}个基因', fontsize=self.font_size + 2, fontweight='bold')
        ax.set_xlabel('突变数量', fontsize=self.font_size)
        ax.set_ylabel('基因', fontsize=self.font_size)
        ax.tick_params(axis='x', labelsize=self.font_size - 1)
        
        # 反转y轴，使频次最高的在顶部
        ax.invert_yaxis()
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self._save_plot(fig, 'gene_mutation_frequency')
        plt.close()
        
        return output_file
    
    def _plot_mutation_quality_distribution(self, mutations: List[MutationRecord]) -> str:
        """绘制突变质量分布直方图"""
        # 提取质量分数
        qualities = [mut.quality for mut in mutations if mut.quality is not None and mut.quality > 0]
        
        if not qualities:
            logger.warning("没有有效的质量分数数据")
            return ""
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 直方图
        ax1.hist(qualities, bins=50, alpha=0.7, color=self.colors['enriched'], edgecolor='black')
        ax1.set_title('突变质量分数分布', fontsize=self.font_size + 1, fontweight='bold')
        ax1.set_xlabel('质量分数', fontsize=self.font_size)
        ax1.set_ylabel('频次', fontsize=self.font_size)
        ax1.tick_params(axis='both', labelsize=self.font_size - 1)
        
        # 添加统计信息
        if self.show_statistics:
            mean_q = np.mean(qualities)
            median_q = np.median(qualities)
            ax1.axvline(mean_q, color='red', linestyle='--', label=f'均值: {mean_q:.1f}')
            ax1.axvline(median_q, color='green', linestyle='--', label=f'中位数: {median_q:.1f}')
            ax1.legend()
        
        # 箱线图
        ax2.boxplot(qualities, vert=True, patch_artist=True,
                   boxprops=dict(facecolor=self.colors['enriched'], alpha=0.7))
        ax2.set_title('突变质量分数箱线图', fontsize=self.font_size + 1, fontweight='bold')
        ax2.set_ylabel('质量分数', fontsize=self.font_size)
        ax2.tick_params(axis='both', labelsize=self.font_size - 1)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self._save_plot(fig, 'mutation_quality_distribution')
        plt.close()
        
        return output_file
    
    def _plot_amino_acid_change_heatmap(self, mutations: List[MutationRecord]) -> Optional[str]:
        """绘制氨基酸变化热图"""
        # 提取氨基酸变化数据
        aa_changes = []
        for mut in mutations:
            if mut.aa_ref and mut.aa_alt and mut.aa_ref != mut.aa_alt:
                aa_changes.append((mut.aa_ref, mut.aa_alt))
        
        if len(aa_changes) < 10:  # 数据太少，不生成热图
            logger.info("氨基酸变化数据不足，跳过热图生成")
            return None
        
        # 创建变化矩阵
        amino_acids = ['A', 'R', 'N', 'D', 'C', 'Q', 'E', 'G', 'H', 'I', 
                      'L', 'K', 'M', 'F', 'P', 'S', 'T', 'W', 'Y', 'V']
        
        change_matrix = pd.DataFrame(0, index=amino_acids, columns=amino_acids)
        
        for aa_ref, aa_alt in aa_changes:
            if aa_ref in amino_acids and aa_alt in amino_acids:
                change_matrix.loc[aa_ref, aa_alt] += 1
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # 绘制热图
        sns.heatmap(change_matrix, annot=True, fmt='d', cmap=self.color_scheme,
                   cbar_kws={'label': '变化次数'}, ax=ax)
        
        ax.set_title('氨基酸变化热图', fontsize=self.font_size + 2, fontweight='bold')
        ax.set_xlabel('变化后氨基酸', fontsize=self.font_size)
        ax.set_ylabel('原始氨基酸', fontsize=self.font_size)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self._save_plot(fig, 'amino_acid_change_heatmap')
        plt.close()
        
        return output_file
    
    def _plot_chromosomal_distribution(self, mutations: List[MutationRecord]) -> str:
        """绘制染色体/contig分布"""
        # 统计染色体分布
        chrom_counts = pd.Series([mut.chromosome for mut in mutations]).value_counts()
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # 绘制条形图
        bars = ax.bar(range(len(chrom_counts)), chrom_counts.values,
                     color=sns.color_palette(self.color_scheme, len(chrom_counts)))
        
        # 设置x轴标签
        ax.set_xticks(range(len(chrom_counts)))
        ax.set_xticklabels(chrom_counts.index, rotation=45, ha='right')
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{int(height)}', ha='center', va='bottom',
                   fontsize=self.font_size - 1, fontweight='bold')
        
        ax.set_title('突变在染色体/Contig上的分布', fontsize=self.font_size + 2, fontweight='bold')
        ax.set_xlabel('染色体/Contig', fontsize=self.font_size)
        ax.set_ylabel('突变数量', fontsize=self.font_size)
        ax.tick_params(axis='both', labelsize=self.font_size - 1)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self._save_plot(fig, 'chromosomal_distribution')
        plt.close()
        
        return output_file
    
    def generate_metabolic_visualizations(self, metabolic_impacts: List[MetabolicImpact]) -> List[str]:
        """生成代谢影响相关的可视化"""
        if not metabolic_impacts:
            logger.warning("没有代谢影响数据，跳过代谢可视化")
            return []
        
        generated_files = []
        
        # 1. 必需基因分布
        essential_plot = self._plot_essential_genes(metabolic_impacts)
        generated_files.append(essential_plot)
        
        # 2. 生长率影响分布
        growth_plot = self._plot_growth_impact_distribution(metabolic_impacts)
        generated_files.append(growth_plot)
        
        # 3. 通路影响汇总
        pathway_impact_plot = self._plot_pathway_impact_summary(metabolic_impacts)
        generated_files.append(pathway_impact_plot)
        
        # 4. 产物产量变化
        product_plot = self._plot_product_yield_changes(metabolic_impacts)
        if product_plot:
            generated_files.append(product_plot)
        
        return generated_files
    
    def _plot_essential_genes(self, metabolic_impacts: List[MetabolicImpact]) -> str:
        """绘制必需基因分析"""
        essential_count = len([imp for imp in metabolic_impacts if imp.is_essential])
        non_essential_count = len(metabolic_impacts) - essential_count
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 饼图：必需 vs 非必需
        sizes = [essential_count, non_essential_count]
        labels = ['必需基因', '非必需基因']
        colors = [self.colors['essential'], self.colors['enriched']]
        
        ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        ax1.set_title('必需基因比例', fontsize=self.font_size + 1, fontweight='bold')
        
        # 条形图：按影响等级分组的必需基因
        impact_levels = ['HIGH', 'MODERATE', 'LOW']
        essential_by_impact = {}
        
        for level in impact_levels:
            essential_by_impact[level] = len([
                imp for imp in metabolic_impacts 
                if imp.impact_level == level and imp.is_essential
            ])
        
        bars = ax2.bar(impact_levels, essential_by_impact.values(),
                      color=[self.colors.get(f'{level.lower()}_impact', '#888888') for level in impact_levels])
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            if height > 0:
                ax2.text(bar.get_x() + bar.get_width()/2., height,
                        f'{int(height)}', ha='center', va='bottom',
                        fontsize=self.font_size, fontweight='bold')
        
        ax2.set_title('按影响等级分组的必需基因数量', fontsize=self.font_size + 1, fontweight='bold')
        ax2.set_xlabel('影响等级', fontsize=self.font_size)
        ax2.set_ylabel('必需基因数量', fontsize=self.font_size)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self._save_plot(fig, 'essential_genes_analysis')
        plt.close()
        
        return output_file
    
    def _plot_growth_impact_distribution(self, metabolic_impacts: List[MetabolicImpact]) -> str:
        """绘制生长率影响分布"""
        # 提取生长率变化数据
        growth_changes = [imp.growth_rate_change for imp in metabolic_impacts 
                         if imp.growth_rate_change != 0.0]
        
        if not growth_changes:
            logger.warning("没有生长率变化数据")
            return ""
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 直方图
        ax1.hist(growth_changes, bins=30, alpha=0.7, color=self.colors['moderate_impact'], 
                edgecolor='black')
        ax1.set_title('生长率变化分布', fontsize=self.font_size + 1, fontweight='bold')
        ax1.set_xlabel('生长率变化', fontsize=self.font_size)
        ax1.set_ylabel('基因数量', fontsize=self.font_size)
        
        # 添加参考线
        ax1.axvline(0, color='red', linestyle='--', label='无变化')
        ax1.axvline(-0.5, color='orange', linestyle='--', label='50%降低')
        ax1.legend()
        
        # 散点图：生长率变化 vs 影响等级
        impact_mapping = {'HIGH': 3, 'MODERATE': 2, 'LOW': 1}
        x_values = [impact_mapping.get(imp.impact_level, 0) for imp in metabolic_impacts 
                   if imp.growth_rate_change != 0.0]
        y_values = growth_changes
        
        scatter = ax2.scatter(x_values, y_values, alpha=0.6, 
                            c=y_values, cmap=self.color_scheme, s=50)
        
        ax2.set_title('生长率变化 vs 突变影响等级', fontsize=self.font_size + 1, fontweight='bold')
        ax2.set_xlabel('影响等级', fontsize=self.font_size)
        ax2.set_ylabel('生长率变化', fontsize=self.font_size)
        ax2.set_xticks([1, 2, 3])
        ax2.set_xticklabels(['LOW', 'MODERATE', 'HIGH'])
        
        # 添加颜色条
        plt.colorbar(scatter, ax=ax2, label='生长率变化')
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self._save_plot(fig, 'growth_impact_distribution')
        plt.close()
        
        return output_file
    
    def _plot_pathway_impact_summary(self, metabolic_impacts: List[MetabolicImpact]) -> str:
        """绘制通路影响汇总"""
        # 统计受影响的通路
        pathway_counts = {}
        for impact in metabolic_impacts:
            for pathway in impact.affected_pathways:
                pathway_counts[pathway] = pathway_counts.get(pathway, 0) + 1
        
        if not pathway_counts:
            logger.warning("没有通路影响数据")
            return ""
        
        # 取前15个受影响最多的通路
        top_pathways = dict(sorted(pathway_counts.items(), key=lambda x: x[1], reverse=True)[:15])
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 绘制水平条形图
        bars = ax.barh(range(len(top_pathways)), list(top_pathways.values()),
                      color=sns.color_palette(self.color_scheme, len(top_pathways)))
        
        # 设置y轴标签
        ax.set_yticks(range(len(top_pathways)))
        ax.set_yticklabels(list(top_pathways.keys()), fontsize=self.font_size - 1)
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax.text(width + 0.1, bar.get_y() + bar.get_height()/2,
                   f'{int(width)}', ha='left', va='center',
                   fontsize=self.font_size - 1, fontweight='bold')
        
        ax.set_title('受突变影响的通路汇总', fontsize=self.font_size + 2, fontweight='bold')
        ax.set_xlabel('受影响基因数量', fontsize=self.font_size)
        ax.set_ylabel('代谢通路', fontsize=self.font_size)
        
        # 反转y轴
        ax.invert_yaxis()
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self._save_plot(fig, 'pathway_impact_summary')
        plt.close()
        
        return output_file
    
    def _plot_product_yield_changes(self, metabolic_impacts: List[MetabolicImpact]) -> Optional[str]:
        """绘制产物产量变化"""
        # 收集产物产量变化数据
        product_changes = {}
        for impact in metabolic_impacts:
            for product, change in impact.product_yield_changes.items():
                if product not in product_changes:
                    product_changes[product] = []
                product_changes[product].append(change)
        
        if not product_changes:
            return None
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 为每个产物创建箱线图数据
        products = list(product_changes.keys())
        changes_data = [product_changes[product] for product in products]
        
        # 绘制箱线图
        box_plot = ax.boxplot(changes_data, labels=products, patch_artist=True)
        
        # 美化箱线图
        colors = sns.color_palette(self.color_scheme, len(products))
        for patch, color in zip(box_plot['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        # 添加零参考线
        ax.axhline(y=0, color='red', linestyle='--', alpha=0.5, label='无变化')
        
        ax.set_title('代谢产物产量变化分布', fontsize=self.font_size + 2, fontweight='bold')
        ax.set_xlabel('代谢产物', fontsize=self.font_size)
        ax.set_ylabel('产量变化比例', fontsize=self.font_size)
        ax.tick_params(axis='both', labelsize=self.font_size - 1)
        ax.legend()
        
        # 旋转x轴标签
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        
        # 保存图片
        output_file = self._save_plot(fig, 'product_yield_changes')
        plt.close()
        
        return output_file
    
    def generate_enrichment_visualizations(self, enrichment_results: List[EnrichmentResult]) -> List[str]:
        """生成富集分析相关的可视化"""
        if not enrichment_results:
            logger.warning("没有富集分析数据，跳过富集可视化")
            return []
        
        generated_files = []
        
        # 1. 火山图
        volcano_plot = self._plot_enrichment_volcano(enrichment_results)
        generated_files.append(volcano_plot)
        
        # 2. 顶部富集通路条形图
        top_enriched_plot = self._plot_top_enriched_pathways(enrichment_results)
        generated_files.append(top_enriched_plot)
        
        # 3. 数据库分布
        database_dist_plot = self._plot_database_distribution(enrichment_results)
        generated_files.append(database_dist_plot)
        
        # 4. 富集倍数分布
        fold_enrichment_plot = self._plot_fold_enrichment_distribution(enrichment_results)
        generated_files.append(fold_enrichment_plot)
        
        # 5. 气泡图
        bubble_plot = self._plot_enrichment_bubble_chart(enrichment_results)
        generated_files.append(bubble_plot)
        
        return generated_files
    
    def _plot_enrichment_volcano(self, enrichment_results: List[EnrichmentResult]) -> str:
        """绘制富集分析火山图"""
        # 准备数据
        fold_enrichments = [result.fold_enrichment for result in enrichment_results]
        p_values = [-np.log10(max(result.adjusted_p_value, 1e-10)) for result in enrichment_results]
        significances = [result.is_significant for result in enrichment_results]
        
        # 创建图表
        fig, ax = plt.subplots(figsize=self.figure_size)
        
        # 分别绘制显著和非显著的点
        significant_x = [fold_enrichments[i] for i, sig in enumerate(significances) if sig]
        significant_y = [p_values[i] for i, sig in enumerate(significances) if sig]
        non_significant_x = [fold_enrichments[i] for i, sig in enumerate(significances) if not sig]
        non_significant_y = [p_values[i] for i, sig in enumerate(significances) if not sig]
        
        # 绘制散点
        ax.scatter(non_significant_x, non_significant_y, c='grey', alpha=0.5, s=30, label='非显著')
        ax.scatter(significant_x, significant_y, c=self.colors['enriched'], alpha=0.7, s=50, label='显著富集')
        
        # 添加参考线
        ax.axvline(x=1, color='red', linestyle='--', alpha=0.5)
        ax.axhline(y=-np.log10(self.config.pathway_analysis.significance_threshold), 
                  color='red', linestyle='--', alpha=0.5)
        
        ax.set_xlabel('富集倍数 (log2)', fontsize=self.font_size)
        ax.set_ylabel('-log10(调整后P值)', fontsize=self.font_size)
        ax.set_title('通路富集分析火山图', fontsize=self.font_size + 2, fontweight='bold')
        ax.legend()
        
        # 转换x轴为log2尺度
        ax.set_xscale('log', base=2)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self._save_plot(fig, 'enrichment_volcano_plot')
        plt.close()
        
        return output_file
    
    def _plot_top_enriched_pathways(self, enrichment_results: List[EnrichmentResult], top_n: int = 20) -> str:
        """绘制顶部富集通路条形图"""
        # 筛选显著富集的通路并排序
        significant_results = [r for r in enrichment_results if r.is_significant]
        significant_results.sort(key=lambda x: x.adjusted_p_value)
        
        top_results = significant_results[:top_n]
        
        if not top_results:
            logger.warning("没有显著富集的通路")
            # 创建空图表
            fig, ax = plt.subplots(figsize=self.figure_size)
            ax.text(0.5, 0.5, '没有显著富集的通路', ha='center', va='center',
                   transform=ax.transAxes, fontsize=self.font_size + 2)
            ax.set_title('顶部富集通路', fontsize=self.font_size + 2, fontweight='bold')
            output_file = self._save_plot(fig, 'top_enriched_pathways')
            plt.close()
            return output_file
        
        # 准备数据
        pathway_names = [r.pathway_name[:50] + '...' if len(r.pathway_name) > 50 else r.pathway_name 
                        for r in top_results]
        fold_enrichments = [r.fold_enrichment for r in top_results]
        p_values = [r.adjusted_p_value for r in top_results]
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(12, max(8, len(top_results) * 0.4)))
        
        # 根据p值设置颜色
        colors = [-np.log10(p) for p in p_values]
        
        # 绘制水平条形图
        bars = ax.barh(range(len(pathway_names)), fold_enrichments, 
                      color=plt.cm.viridis(np.array(colors) / max(colors)))
        
        # 设置y轴标签
        ax.set_yticks(range(len(pathway_names)))
        ax.set_yticklabels(pathway_names, fontsize=self.font_size - 2)
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax.text(width + 0.1, bar.get_y() + bar.get_height()/2,
                   f'{width:.1f}', ha='left', va='center',
                   fontsize=self.font_size - 2, fontweight='bold')
        
        ax.set_xlabel('富集倍数', fontsize=self.font_size)
        ax.set_title(f'顶部{len(top_results)}个显著富集通路', fontsize=self.font_size + 2, fontweight='bold')
        
        # 反转y轴
        ax.invert_yaxis()
        
        # 添加颜色条
        sm = plt.cm.ScalarMappable(cmap=plt.cm.viridis, 
                                  norm=plt.Normalize(vmin=min(colors), vmax=max(colors)))
        sm.set_array([])
        cbar = plt.colorbar(sm, ax=ax)
        cbar.set_label('-log10(调整后P值)', fontsize=self.font_size - 1)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self._save_plot(fig, 'top_enriched_pathways')
        plt.close()
        
        return output_file
    
    def _plot_database_distribution(self, enrichment_results: List[EnrichmentResult]) -> str:
        """绘制数据库分布"""
        # 统计各数据库的通路数量
        db_stats = {}
        for result in enrichment_results:
            db = result.database
            if db not in db_stats:
                db_stats[db] = {'total': 0, 'significant': 0}
            db_stats[db]['total'] += 1
            if result.is_significant:
                db_stats[db]['significant'] += 1
        
        # 准备数据
        databases = list(db_stats.keys())
        total_counts = [db_stats[db]['total'] for db in databases]
        significant_counts = [db_stats[db]['significant'] for db in databases]
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 左图：总通路数分布
        ax1.pie(total_counts, labels=databases, autopct='%1.1f%%', startangle=90,
               colors=sns.color_palette(self.color_scheme, len(databases)))
        ax1.set_title('数据库通路分布', fontsize=self.font_size + 1, fontweight='bold')
        
        # 右图：显著通路比较
        x = np.arange(len(databases))
        width = 0.35
        
        bars1 = ax2.bar(x - width/2, total_counts, width, label='总通路数', alpha=0.7,
                       color=self.colors['enriched'])
        bars2 = ax2.bar(x + width/2, significant_counts, width, label='显著富集', alpha=0.7,
                       color=self.colors['high_impact'])
        
        # 添加数值标签
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height,
                        f'{int(height)}', ha='center', va='bottom',
                        fontsize=self.font_size - 1, fontweight='bold')
        
        ax2.set_xlabel('数据库', fontsize=self.font_size)
        ax2.set_ylabel('通路数量', fontsize=self.font_size)
        ax2.set_title('各数据库富集结果比较', fontsize=self.font_size + 1, fontweight='bold')
        ax2.set_xticks(x)
        ax2.set_xticklabels(databases)
        ax2.legend()
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self._save_plot(fig, 'database_distribution')
        plt.close()
        
        return output_file
    
    def _plot_fold_enrichment_distribution(self, enrichment_results: List[EnrichmentResult]) -> str:
        """绘制富集倍数分布"""
        # 提取富集倍数
        fold_enrichments = [r.fold_enrichment for r in enrichment_results if r.fold_enrichment < 100]  # 过滤极值
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 直方图
        ax1.hist(fold_enrichments, bins=30, alpha=0.7, color=self.colors['enriched'], edgecolor='black')
        ax1.set_xlabel('富集倍数', fontsize=self.font_size)
        ax1.set_ylabel('通路数量', fontsize=self.font_size)
        ax1.set_title('富集倍数分布', fontsize=self.font_size + 1, fontweight='bold')
        
        # 添加参考线
        ax1.axvline(1, color='red', linestyle='--', label='无富集')
        ax1.axvline(2, color='orange', linestyle='--', label='2倍富集')
        ax1.legend()
        
        # 箱线图：按数据库分组
        databases = list(set(r.database for r in enrichment_results))
        db_enrichments = []
        for db in databases:
            db_data = [r.fold_enrichment for r in enrichment_results 
                      if r.database == db and r.fold_enrichment < 100]
            db_enrichments.append(db_data)
        
        box_plot = ax2.boxplot(db_enrichments, labels=databases, patch_artist=True)
        
        # 美化箱线图
        colors = sns.color_palette(self.color_scheme, len(databases))
        for patch, color in zip(box_plot['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        ax2.set_xlabel('数据库', fontsize=self.font_size)
        ax2.set_ylabel('富集倍数', fontsize=self.font_size)
        ax2.set_title('各数据库富集倍数分布', fontsize=self.font_size + 1, fontweight='bold')
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self._save_plot(fig, 'fold_enrichment_distribution')
        plt.close()
        
        return output_file
    
    def _plot_enrichment_bubble_chart(self, enrichment_results: List[EnrichmentResult], top_n: int = 30) -> str:
        """绘制富集分析气泡图"""
        # 筛选显著结果
        significant_results = [r for r in enrichment_results if r.is_significant][:top_n]
        
        if not significant_results:
            return ""
        
        # 准备数据
        x = [r.fold_enrichment for r in significant_results]
        y = [-np.log10(r.adjusted_p_value) for r in significant_results]
        sizes = [r.mutated_genes_in_pathway * 10 for r in significant_results]  # 基因数决定气泡大小
        colors = [r.database for r in significant_results]
        
        # 创建图表
        fig, ax = plt.subplots(figsize=self.figure_size)
        
        # 为不同数据库设置不同颜色
        unique_dbs = list(set(colors))
        color_map = {db: sns.color_palette(self.color_scheme, len(unique_dbs))[i] 
                    for i, db in enumerate(unique_dbs)}
        
        # 绘制气泡图
        for db in unique_dbs:
            db_indices = [i for i, c in enumerate(colors) if c == db]
            db_x = [x[i] for i in db_indices]
            db_y = [y[i] for i in db_indices]
            db_sizes = [sizes[i] for i in db_indices]
            
            ax.scatter(db_x, db_y, s=db_sizes, c=[color_map[db]], alpha=0.6, label=db)
        
        ax.set_xlabel('富集倍数 (log2)', fontsize=self.font_size)
        ax.set_ylabel('-log10(调整后P值)', fontsize=self.font_size)
        ax.set_title('通路富集分析气泡图', fontsize=self.font_size + 2, fontweight='bold')
        ax.set_xscale('log', base=2)
        ax.legend(title='数据库', bbox_to_anchor=(1.05, 1), loc='upper left')
        
        # 添加参考线
        ax.axvline(x=1, color='red', linestyle='--', alpha=0.5)
        ax.axhline(y=-np.log10(self.config.pathway_analysis.significance_threshold), 
                  color='red', linestyle='--', alpha=0.5)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self._save_plot(fig, 'enrichment_bubble_chart')
        plt.close()
        
        return output_file
    
    def generate_network_visualizations(self, mutations: List[MutationRecord],
                                      enrichment_results: List[EnrichmentResult],
                                      annotation_data: Dict) -> List[str]:
        """生成网络可视化（需要networkx）"""
        if not NETWORK_AVAILABLE:
            logger.warning("网络分析库未安装，跳过网络可视化")
            return []
        
        generated_files = []
        
        try:
            # 1. 基因-通路网络
            gene_pathway_network = self._plot_gene_pathway_network(mutations, enrichment_results)
            if gene_pathway_network:
                generated_files.append(gene_pathway_network)
            
            # 2. 蛋白质相互作用网络（如果有数据）
            if 'protein_interactions' in annotation_data:
                ppi_network = self._plot_protein_interaction_network(mutations, annotation_data)
                if ppi_network:
                    generated_files.append(ppi_network)
            
        except Exception as e:
            logger.error(f"生成网络可视化时出错: {e}")
        
        return generated_files
    
    def _plot_gene_pathway_network(self, mutations: List[MutationRecord],
                                 enrichment_results: List[EnrichmentResult]) -> Optional[str]:
        """绘制基因-通路网络图"""
        try:
            import networkx as nx
            
            # 创建网络图
            G = nx.Graph()
            
            # 添加基因节点
            mutated_genes = set(mut.gene_id for mut in mutations)
            for gene in mutated_genes:
                G.add_node(gene, node_type='gene', size=20)
            
            # 添加通路节点和边
            significant_pathways = [r for r in enrichment_results if r.is_significant][:20]  # 限制数量
            
            for pathway in significant_pathways:
                pathway_id = pathway.pathway_id
                G.add_node(pathway_id, node_type='pathway', size=50)
                
                # 连接基因和通路
                for gene in pathway.mutated_genes_list:
                    if gene in mutated_genes:
                        G.add_edge(gene, pathway_id)
            
            if len(G.nodes()) == 0:
                return None
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(15, 12))
            
            # 设置布局
            pos = nx.spring_layout(G, k=1, iterations=50)
            
            # 分别绘制基因和通路节点
            gene_nodes = [n for n, d in G.nodes(data=True) if d['node_type'] == 'gene']
            pathway_nodes = [n for n, d in G.nodes(data=True) if d['node_type'] == 'pathway']
            
            # 绘制节点
            nx.draw_networkx_nodes(G, pos, nodelist=gene_nodes, 
                                 node_color=self.colors['high_impact'], 
                                 node_size=300, alpha=0.7, ax=ax)
            nx.draw_networkx_nodes(G, pos, nodelist=pathway_nodes,
                                 node_color=self.colors['enriched'],
                                 node_size=500, alpha=0.7, ax=ax)
            
            # 绘制边
            nx.draw_networkx_edges(G, pos, alpha=0.3, ax=ax)
            
            # 添加标签（只显示部分以避免拥挤）
            if len(G.nodes()) < 50:
                nx.draw_networkx_labels(G, pos, font_size=8, ax=ax)
            
            ax.set_title('基因-通路网络图', fontsize=self.font_size + 2, fontweight='bold')
            ax.axis('off')
            
            # 添加图例
            gene_patch = mpatches.Patch(color=self.colors['high_impact'], label='突变基因')
            pathway_patch = mpatches.Patch(color=self.colors['enriched'], label='富集通路')
            ax.legend(handles=[gene_patch, pathway_patch])
            
            plt.tight_layout()
            
            # 保存图片
            output_file = self._save_plot(fig, 'gene_pathway_network')
            plt.close()
            
            return output_file
            
        except Exception as e:
            logger.error(f"绘制基因-通路网络图时出错: {e}")
            return None
    
    def generate_summary_visualizations(self, mutations: List[MutationRecord],
                                      metabolic_impacts: List[MetabolicImpact],
                                      enrichment_results: List[EnrichmentResult]) -> List[str]:
        """生成综合摘要可视化"""
        generated_files = []
        
        # 1. 综合摘要仪表板
        dashboard = self._create_summary_dashboard(mutations, metabolic_impacts, enrichment_results)
        generated_files.append(dashboard)
        
        # 2. Wood-Ljungdahl通路特异性分析
        wlp_analysis = self._plot_wood_ljungdahl_analysis(mutations, metabolic_impacts)
        if wlp_analysis:
            generated_files.append(wlp_analysis)
        
        return generated_files
    
    def _create_summary_dashboard(self, mutations: List[MutationRecord],
                                metabolic_impacts: List[MetabolicImpact],
                                enrichment_results: List[EnrichmentResult]) -> str:
        """创建综合摘要仪表板"""
        # 创建多子图布局
        fig = plt.figure(figsize=(20, 15))
        gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)
        
        # 1. 突变影响等级分布
        ax1 = fig.add_subplot(gs[0, 0])
        impact_counts = pd.Series([mut.impact_level for mut in mutations]).value_counts()
        ax1.pie(impact_counts.values, labels=impact_counts.index, autopct='%1.1f%%')
        ax1.set_title('突变影响等级', fontweight='bold')
        
        # 2. 变异类型分布
        ax2 = fig.add_subplot(gs[0, 1])
        variant_counts = pd.Series([mut.variant_type for mut in mutations]).value_counts()
        ax2.bar(variant_counts.index, variant_counts.values)
        ax2.set_title('变异类型分布', fontweight='bold')
        ax2.tick_params(axis='x', rotation=45)
        
        # 3. 顶部突变基因
        ax3 = fig.add_subplot(gs[0, 2:])
        gene_counts = pd.Series([mut.gene_id for mut in mutations]).value_counts().head(10)
        ax3.barh(range(len(gene_counts)), gene_counts.values)
        ax3.set_yticks(range(len(gene_counts)))
        ax3.set_yticklabels(gene_counts.index)
        ax3.set_title('突变频次最高的10个基因', fontweight='bold')
        ax3.invert_yaxis()
        
        # 4. 必需基因分析
        if metabolic_impacts:
            ax4 = fig.add_subplot(gs[1, 0])
            essential_count = len([imp for imp in metabolic_impacts if imp.is_essential])
            non_essential = len(metabolic_impacts) - essential_count
            ax4.pie([essential_count, non_essential], labels=['必需', '非必需'], autopct='%1.1f%%')
            ax4.set_title('必需基因比例', fontweight='bold')
            
            # 5. 生长率影响
            ax5 = fig.add_subplot(gs[1, 1])
            growth_changes = [imp.growth_rate_change for imp in metabolic_impacts if imp.growth_rate_change != 0]
            if growth_changes:
                ax5.hist(growth_changes, bins=20, alpha=0.7)
                ax5.axvline(0, color='red', linestyle='--')
                ax5.set_title('生长率变化分布', fontweight='bold')
                ax5.set_xlabel('生长率变化')
        
        # 6. 富集分析结果
        if enrichment_results:
            ax6 = fig.add_subplot(gs[1, 2:])
            significant = [r for r in enrichment_results if r.is_significant][:10]
            if significant:
                pathway_names = [r.pathway_name[:30] + '...' if len(r.pathway_name) > 30 else r.pathway_name 
                               for r in significant]
                fold_enrichments = [r.fold_enrichment for r in significant]
                ax6.barh(range(len(pathway_names)), fold_enrichments)
                ax6.set_yticks(range(len(pathway_names)))
                ax6.set_yticklabels(pathway_names)
                ax6.set_title('顶部富集通路', fontweight='bold')
                ax6.invert_yaxis()
        
        # 7. 数据库统计
        ax7 = fig.add_subplot(gs[2, 0])
        if enrichment_results:
            db_counts = pd.Series([r.database for r in enrichment_results]).value_counts()
            ax7.pie(db_counts.values, labels=db_counts.index, autopct='%1.1f%%')
            ax7.set_title('数据库分布', fontweight='bold')
        
        # 8. 关键统计指标
        ax8 = fig.add_subplot(gs[2, 1:])
        ax8.axis('off')
        
        # 准备统计文本
        stats_text = []
        stats_text.append(f"总突变数: {len(mutations)}")
        stats_text.append(f"受影响基因数: {len(set(mut.gene_id for mut in mutations))}")
        
        if metabolic_impacts:
            stats_text.append(f"必需基因数: {len([imp for imp in metabolic_impacts if imp.is_essential])}")
        
        if enrichment_results:
            significant_count = len([r for r in enrichment_results if r.is_significant])
            stats_text.append(f"显著富集通路: {significant_count}")
        
        # 显示统计信息
        for i, text in enumerate(stats_text):
            ax8.text(0.1, 0.8 - i*0.15, text, transform=ax8.transAxes, 
                    fontsize=16, fontweight='bold')
        
        ax8.set_title('关键统计指标', fontweight='bold', fontsize=18)
        
        # 整体标题
        fig.suptitle('梭菌突变分析综合摘要仪表板', fontsize=24, fontweight='bold', y=0.95)
        
        # 保存图片
        output_file = self._save_plot(fig, 'summary_dashboard')
        plt.close()
        
        return output_file
    
    def _plot_wood_ljungdahl_analysis(self, mutations: List[MutationRecord],
                                    metabolic_impacts: List[MetabolicImpact]) -> Optional[str]:
        """绘制Wood-Ljungdahl通路特异性分析"""
        # Wood-Ljungdahl通路关键基因
        wlp_keywords = [
            'formate dehydrogenase', 'formyl', 'methyl', 'carbonyl',
            'acetyl-coa synthase', 'carbon monoxide dehydrogenase'
        ]
        
        # 识别WLP相关的突变
        wlp_mutations = []
        for mut in mutations:
            # 这里需要结合注释信息来判断
            # 简化处理：基于基因名称或描述
            gene_desc = mut.gene_name.lower() if mut.gene_name else ""
            if any(keyword in gene_desc for keyword in wlp_keywords):
                wlp_mutations.append(mut)
        
        if len(wlp_mutations) < 3:  # 数据太少
            return None
        
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. WLP基因突变分布
        wlp_gene_counts = pd.Series([mut.gene_id for mut in wlp_mutations]).value_counts()
        ax1.bar(range(len(wlp_gene_counts)), wlp_gene_counts.values)
        ax1.set_xticks(range(len(wlp_gene_counts)))
        ax1.set_xticklabels(wlp_gene_counts.index, rotation=45, ha='right')
        ax1.set_title('Wood-Ljungdahl通路基因突变分布', fontweight='bold')
        ax1.set_ylabel('突变数量')
        
        # 2. WLP突变影响等级
        wlp_impact_counts = pd.Series([mut.impact_level for mut in wlp_mutations]).value_counts()
        colors = [self.colors.get(f'{level.lower()}_impact', '#888888') for level in wlp_impact_counts.index]
        ax2.pie(wlp_impact_counts.values, labels=wlp_impact_counts.index, 
               colors=colors, autopct='%1.1f%%')
        ax2.set_title('WLP突变影响等级分布', fontweight='bold')
        
        # 3. WLP代谢影响（如果有数据）
        if metabolic_impacts:
            wlp_genes = set(mut.gene_id for mut in wlp_mutations)
            wlp_metabolic_impacts = [imp for imp in metabolic_impacts if imp.gene_id in wlp_genes]
            
            if wlp_metabolic_impacts:
                growth_changes = [imp.growth_rate_change for imp in wlp_metabolic_impacts 
                                if imp.growth_rate_change != 0]
                if growth_changes:
                    ax3.hist(growth_changes, bins=10, alpha=0.7, color=self.colors['wlp_pathway'])
                    ax3.axvline(0, color='red', linestyle='--')
                    ax3.set_title('WLP基因生长率影响', fontweight='bold')
                    ax3.set_xlabel('生长率变化')
                    ax3.set_ylabel('基因数量')
        
        # 4. WLP通路示意图（简化）
        ax4.axis('off')
        ax4.set_title('Wood-Ljungdahl通路关键步骤', fontweight='bold')
        
        # 简化的通路步骤文本
        pathway_steps = [
            "CO₂ → 甲酸 (FDH)",
            "甲酸 → 甲酰-THF (FHS)", 
            "甲酰-THF → 甲基-THF (MetF)",
            "甲基-THF + CO → 乙酰辅酶A (ACS)",
            "乙酰辅酶A → 乙酸/乙醇"
        ]
        
        for i, step in enumerate(pathway_steps):
            ax4.text(0.1, 0.8 - i*0.15, f"{i+1}. {step}", transform=ax4.transAxes,
                    fontsize=12, fontweight='bold')
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self._save_plot(fig, 'wood_ljungdahl_analysis')
        plt.close()
        
        return output_file
    
    def generate_interactive_html_report(self, mutations: List[MutationRecord],
                                       metabolic_impacts: List[MetabolicImpact],
                                       enrichment_results: List[EnrichmentResult]) -> str:
        """生成交互式HTML可视化报告"""
        try:
            # 创建子图
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('突变影响等级分布', '变异类型分布', 
                              '顶部富集通路', '基因突变频次'),
                specs=[[{"type": "pie"}, {"type": "bar"}],
                      [{"type": "bar"}, {"type": "bar"}]]
            )
            
            # 1. 突变影响等级分布饼图
            impact_counts = pd.Series([mut.impact_level for mut in mutations]).value_counts()
            fig.add_trace(
                go.Pie(labels=impact_counts.index, values=impact_counts.values,
                      name="影响等级"),
                row=1, col=1
            )
            
            # 2. 变异类型分布条形图
            variant_counts = pd.Series([mut.variant_type for mut in mutations]).value_counts()
            fig.add_trace(
                go.Bar(x=variant_counts.index, y=variant_counts.values,
                      name="变异类型"),
                row=1, col=2
            )
            
            # 3. 顶部富集通路
            if enrichment_results:
                significant = [r for r in enrichment_results if r.is_significant][:10]
                if significant:
                    pathway_names = [r.pathway_name[:30] + '...' if len(r.pathway_name) > 30 
                                   else r.pathway_name for r in significant]
                    fold_enrichments = [r.fold_enrichment for r in significant]
                    
                    fig.add_trace(
                        go.Bar(x=fold_enrichments, y=pathway_names, 
                              orientation='h', name="富集倍数"),
                        row=2, col=1
                    )
            
            # 4. 基因突变频次
            gene_counts = pd.Series([mut.gene_id for mut in mutations]).value_counts().head(10)
            fig.add_trace(
                go.Bar(x=gene_counts.values, y=gene_counts.index,
                      orientation='h', name="突变次数"),
                row=2, col=2
            )
            
            # 更新布局
            fig.update_layout(
                title_text="梭菌突变分析交互式报告",
                title_x=0.5,
                height=800,
                showlegend=False
            )
            
            # 保存HTML文件
            html_file = self.output_dir / 'interactive_report.html'
            fig.write_html(str(html_file))
            
            logger.info(f"交互式HTML报告已生成: {html_file}")
            return str(html_file)
            
        except Exception as e:
            logger.error(f"生成交互式HTML报告时出错: {e}")
            return ""
    
    def _save_plot(self, fig, filename: str) -> str:
        """保存图表到多种格式"""
        saved_files = []
        
        for fmt in self.plot_formats:
            output_file = self.output_dir / f"{filename}.{fmt}"
            
            try:
                if fmt.lower() == 'pdf':
                    fig.savefig(output_file, format='pdf', dpi=self.plot_dpi, 
                               bbox_inches='tight', facecolor='white')
                elif fmt.lower() == 'png':
                    fig.savefig(output_file, format='png', dpi=self.plot_dpi,
                               bbox_inches='tight', facecolor='white')
                elif fmt.lower() == 'svg':
                    fig.savefig(output_file, format='svg', dpi=self.plot_dpi,
                               bbox_inches='tight', facecolor='white')
                elif fmt.lower() == 'eps':
                    fig.savefig(output_file, format='eps', dpi=self.plot_dpi,
                               bbox_inches='tight', facecolor='white')
                else:
                    logger.warning(f"不支持的图片格式: {fmt}")
                    continue
                
                saved_files.append(str(output_file))
                logger.debug(f"图表已保存: {output_file}")
                
            except Exception as e:
                logger.error(f"保存图表 {filename}.{fmt} 时出错: {e}")
                continue
        
        # 返回第一个成功保存的文件路径
        return saved_files[0] if saved_files else ""
    
    def create_publication_figures(self, mutations: List[MutationRecord],
                                 metabolic_impacts: List[MetabolicImpact],
                                 enrichment_results: List[EnrichmentResult]) -> Dict[str, str]:
        """创建适合发表的高质量图表"""
        logger.info("创建发表级别的高质量图表...")
        
        # 设置发表级别的样式
        original_style = plt.rcParams.copy()
        
        # 发表级别设置
        plt.rcParams.update({
            'font.size': 14,
            'axes.titlesize': 16,
            'axes.labelsize': 14,
            'xtick.labelsize': 12,
            'ytick.labelsize': 12,
            'legend.fontsize': 12,
            'figure.titlesize': 18,
            'lines.linewidth': 2,
            'lines.markersize': 8,
            'patch.linewidth': 0.5,
            'axes.linewidth': 1.5,
            'grid.linewidth': 0.8,
            'font.family': 'sans-serif',
            'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans'],
            'pdf.fonttype': 42,  # 确保字体嵌入PDF
            'ps.fonttype': 42
        })
        
        publication_figures = {}
        
        try:
            # 图1: 突变概览（适合主图）
            fig1 = self._create_publication_figure_1(mutations)
            pub_file1 = self._save_publication_figure(fig1, 'Figure1_MutationOverview')
            publication_figures['Figure1'] = pub_file1
            plt.close(fig1)
            
            # 图2: 富集分析（适合主图）
            if enrichment_results:
                fig2 = self._create_publication_figure_2(enrichment_results)
                pub_file2 = self._save_publication_figure(fig2, 'Figure2_EnrichmentAnalysis')
                publication_figures['Figure2'] = pub_file2
                plt.close(fig2)
            
            # 图3: 代谢影响（补充材料）
            if metabolic_impacts:
                fig3 = self._create_publication_figure_3(metabolic_impacts)
                pub_file3 = self._save_publication_figure(fig3, 'Figure3_MetabolicImpact')
                publication_figures['Figure3'] = pub_file3
                plt.close(fig3)
            
            logger.info(f"创建了 {len(publication_figures)} 个发表级别图表")
            
        finally:
            # 恢复原始样式
            plt.rcParams.update(original_style)
        
        return publication_figures
    
    def _create_publication_figure_1(self, mutations: List[MutationRecord]) -> plt.Figure:
        """创建发表级别图1：突变概览"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
        
        # A. 突变影响等级分布
        impact_counts = pd.Series([mut.impact_level for mut in mutations]).value_counts()
        colors = ['#d62728', '#ff7f0e', '#2ca02c', '#9467bd'][:len(impact_counts)]
        
        wedges, texts, autotexts = ax1.pie(impact_counts.values, labels=impact_counts.index,
                                          colors=colors, autopct='%1.1f%%', startangle=90)
        ax1.set_title('A. Mutation Impact Distribution', fontweight='bold', pad=20)
        
        # B. 变异类型分布
        variant_counts = pd.Series([mut.variant_type for mut in mutations]).value_counts()
        bars = ax2.bar(variant_counts.index, variant_counts.values, color=['#1f77b4', '#ff7f0e'])
        ax2.set_title('B. Variant Type Distribution', fontweight='bold')
        ax2.set_ylabel('Number of Mutations')
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{int(height)}', ha='center', va='bottom', fontweight='bold')
        
        # C. 突变质量分布
        qualities = [mut.quality for mut in mutations if mut.quality and mut.quality > 0]
        ax3.hist(qualities, bins=30, alpha=0.7, color='#2ca02c', edgecolor='black')
        ax3.set_title('C. Mutation Quality Distribution', fontweight='bold')
        ax3.set_xlabel('QUAL Score')
        ax3.set_ylabel('Frequency')
        
        # 添加统计线
        mean_q = np.mean(qualities)
        ax3.axvline(mean_q, color='red', linestyle='--', linewidth=2, label=f'Mean: {mean_q:.1f}')
        ax3.legend()
        
        # D. 顶部突变基因
        gene_counts = pd.Series([mut.gene_id for mut in mutations]).value_counts().head(8)
        bars = ax4.barh(range(len(gene_counts)), gene_counts.values, color='#ff7f0e')
        ax4.set_yticks(range(len(gene_counts)))
        ax4.set_yticklabels([f"{gene[:15]}..." if len(gene) > 15 else gene 
                            for gene in gene_counts.index])
        ax4.set_title('D. Top Mutated Genes', fontweight='bold')
        ax4.set_xlabel('Number of Mutations')
        ax4.invert_yaxis()
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax4.text(width + 0.1, bar.get_y() + bar.get_height()/2,
                    f'{int(width)}', ha='left', va='center', fontweight='bold')
        
        plt.tight_layout()
        return fig
    
    def _create_publication_figure_2(self, enrichment_results: List[EnrichmentResult]) -> plt.Figure:
        """创建发表级别图2：富集分析"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 12))
        
        significant_results = [r for r in enrichment_results if r.is_significant]
        
        # A. 火山图
        fold_enrichments = [r.fold_enrichment for r in enrichment_results]
        p_values = [-np.log10(max(r.adjusted_p_value, 1e-10)) for r in enrichment_results]
        
        # 分别绘制显著和非显著的点
        for i, result in enumerate(enrichment_results):
            color = '#d62728' if result.is_significant else '#cccccc'
            alpha = 0.8 if result.is_significant else 0.3
            ax1.scatter(fold_enrichments[i], p_values[i], c=color, alpha=alpha, s=40)
        
        ax1.set_xlabel('Fold Enrichment (log₂)', fontweight='bold')
        ax1.set_ylabel('-log₁₀(Adjusted P-value)', fontweight='bold')
        ax1.set_title('A. Pathway Enrichment Volcano Plot', fontweight='bold')
        ax1.set_xscale('log', base=2)
        
        # 添加参考线
        ax1.axvline(x=1, color='black', linestyle='--', alpha=0.5)
        ax1.axhline(y=-np.log10(0.05), color='black', linestyle='--', alpha=0.5)
        
        # B. 顶部富集通路
        top_pathways = significant_results[:12]
        if top_pathways:
            pathway_names = [p.pathway_name[:40] + '...' if len(p.pathway_name) > 40 
                            else p.pathway_name for p in top_pathways]
            fold_enrichments = [p.fold_enrichment for p in top_pathways]
            p_values = [p.adjusted_p_value for p in top_pathways]
            
            # 根据p值设置颜色
            colors = plt.cm.viridis([-np.log10(p) for p in p_values])
            
            bars = ax2.barh(range(len(pathway_names)), fold_enrichments, color=colors)
            ax2.set_yticks(range(len(pathway_names)))
            ax2.set_yticklabels(pathway_names, fontsize=10)
            ax2.set_xlabel('Fold Enrichment', fontweight='bold')
            ax2.set_title('B. Top Enriched Pathways', fontweight='bold')
            ax2.invert_yaxis()
        
        # C. 数据库分布
        db_stats = defaultdict(lambda: {'total': 0, 'significant': 0})
        for result in enrichment_results:
            db_stats[result.database]['total'] += 1
            if result.is_significant:
                db_stats[result.database]['significant'] += 1
        
        databases = list(db_stats.keys())
        total_counts = [db_stats[db]['total'] for db in databases]
        significant_counts = [db_stats[db]['significant'] for db in databases]
        
        x = np.arange(len(databases))
        width = 0.35
        
        bars1 = ax3.bar(x - width/2, total_counts, width, label='Total Pathways', 
                       color='lightblue', alpha=0.7)
        bars2 = ax3.bar(x + width/2, significant_counts, width, label='Significant', 
                       color='darkblue', alpha=0.7)
        
        ax3.set_xlabel('Database', fontweight='bold')
        ax3.set_ylabel('Number of Pathways', fontweight='bold')
        ax3.set_title('C. Database Distribution', fontweight='bold')
        ax3.set_xticks(x)
        ax3.set_xticklabels(databases)
        ax3.legend()
        
        # D. 富集倍数分布
        fold_enrichments = [r.fold_enrichment for r in significant_results if r.fold_enrichment < 20]
        ax4.hist(fold_enrichments, bins=15, alpha=0.7, color='#2ca02c', edgecolor='black')
        ax4.set_xlabel('Fold Enrichment', fontweight='bold')
        ax4.set_ylabel('Number of Pathways', fontweight='bold')
        ax4.set_title('D. Enrichment Fold Distribution', fontweight='bold')
        
        # 添加统计线
        if fold_enrichments:
            mean_fold = np.mean(fold_enrichments)
            ax4.axvline(mean_fold, color='red', linestyle='--', linewidth=2, 
                       label=f'Mean: {mean_fold:.1f}')
            ax4.legend()
        
        plt.tight_layout()
        return fig
    
    def _create_publication_figure_3(self, metabolic_impacts: List[MetabolicImpact]) -> plt.Figure:
        """创建发表级别图3：代谢影响"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
        
        # A. 必需基因分析
        essential_count = len([imp for imp in metabolic_impacts if imp.is_essential])
        non_essential = len(metabolic_impacts) - essential_count
        
        sizes = [essential_count, non_essential]
        labels = ['Essential', 'Non-essential']
        colors = ['#d62728', '#2ca02c']
        
        ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        ax1.set_title('A. Essential Gene Analysis', fontweight='bold')
        
        # B. 生长率影响分布
        growth_changes = [imp.growth_rate_change for imp in metabolic_impacts 
                         if abs(imp.growth_rate_change) > 0.01]  # 过滤微小变化
        
        if growth_changes:
            ax2.hist(growth_changes, bins=20, alpha=0.7, color='#ff7f0e', edgecolor='black')
            ax2.axvline(0, color='red', linestyle='--', linewidth=2, label='No Change')
            ax2.set_xlabel('Growth Rate Change', fontweight='bold')
            ax2.set_ylabel('Number of Genes', fontweight='bold')
            ax2.set_title('B. Growth Rate Impact', fontweight='bold')
            ax2.legend()
        
        # C. 通路影响汇总
        pathway_counts = defaultdict(int)
        for impact in metabolic_impacts:
            for pathway in impact.affected_pathways:
                pathway_counts[pathway] += 1
        
        if pathway_counts:
            top_pathways = dict(sorted(pathway_counts.items(), key=lambda x: x[1], reverse=True)[:10])
            
            bars = ax3.barh(range(len(top_pathways)), list(top_pathways.values()),
                           color='#9467bd', alpha=0.7)
            ax3.set_yticks(range(len(top_pathways)))
            ax3.set_yticklabels([p.replace('_', ' ') for p in top_pathways.keys()])
            ax3.set_xlabel('Number of Affected Genes', fontweight='bold')
            ax3.set_title('C. Pathway Impact Summary', fontweight='bold')
            ax3.invert_yaxis()
        
        # D. 影响等级vs生长率变化
        impact_mapping = {'HIGH': 3, 'MODERATE': 2, 'LOW': 1}
        x_values = [impact_mapping.get(imp.impact_level, 0) for imp in metabolic_impacts]
        y_values = [imp.growth_rate_change for imp in metabolic_impacts]
        
        scatter = ax4.scatter(x_values, y_values, alpha=0.6, c=y_values, 
                             cmap='RdYlGn_r', s=60, edgecolors='black', linewidth=0.5)
        
        ax4.set_xlabel('Mutation Impact Level', fontweight='bold')
        ax4.set_ylabel('Growth Rate Change', fontweight='bold')
        ax4.set_title('D. Impact Level vs Growth Effect', fontweight='bold')
        ax4.set_xticks([1, 2, 3])
        ax4.set_xticklabels(['LOW', 'MODERATE', 'HIGH'])
        
        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=ax4)
        cbar.set_label('Growth Rate Change', fontweight='bold')
        
        plt.tight_layout()
        return fig
    
    def _save_publication_figure(self, fig: plt.Figure, filename: str) -> str:
        """保存发表级别的图表"""
        # 发表级别通常需要高分辨率
        pub_dir = self.output_dir / 'publication_figures'
        pub_dir.mkdir(exist_ok=True)
        
        # 保存多种格式
        formats = {'pdf': 600, 'png': 600, 'svg': None, 'eps': 600}
        saved_files = []
        
        for fmt, dpi in formats.items():
            output_file = pub_dir / f"{filename}.{fmt}"
            
            try:
                save_kwargs = {
                    'bbox_inches': 'tight',
                    'facecolor': 'white',
                    'edgecolor': 'none',
                    'transparent': False
                }
                
                if dpi:
                    save_kwargs['dpi'] = dpi
                
                fig.savefig(output_file, format=fmt, **save_kwargs)
                saved_files.append(str(output_file))
                
            except Exception as e:
                logger.error(f"保存发表级别图表 {filename}.{fmt} 时出错: {e}")
        
        return saved_files[0] if saved_files else ""


# 便捷函数
def generate_visualizations(mutations: List[MutationRecord],
                          metabolic_impacts: List[MetabolicImpact],
                          enrichment_results: List[EnrichmentResult],
                          config: ConfigManager,
                          annotation_data: Optional[Dict] = None) -> Dict[str, List[str]]:
    """
    生成所有可视化的便捷函数
    
    参数:
        mutations: 突变记录列表
        metabolic_impacts: 代谢影响列表
        enrichment_results: 富集分析结果列表
        config: 配置管理器
        annotation_data: 注释数据（可选）
        
    返回:
        生成的图表文件路径字典
    """
    visualizer = VisualizationGenerator(config)
    return visualizer.generate_all_visualizations(
        mutations, metabolic_impacts, enrichment_results, annotation_data
    )


if __name__ == '__main__':
    # 测试代码
    import argparse
    
    parser = argparse.ArgumentParser(description='可视化生成器测试')
    parser.add_argument('--config', default='config/default_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--test', action='store_true',
                       help='运行测试模式')
    
    args = parser.parse_args()
    
    if args.test:
        print("可视化生成器测试模式")
        print("请使用完整工作流程进行测试")
    else:
        print("请使用 --test 参数运行测试模式")