#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块 - 梭菌生物信息学工作流程
作者：生物信息学分析团队
版本：3.0.0
描述：管理工作流程的所有配置参数，支持YAML配置文件和默认参数，确保所有数据来源真实可靠
"""

import os
import yaml
import json
import logging
import requests
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field, asdict
from datetime import datetime
import urllib.parse

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """数据库连接配置 - 连接真实的生物信息学数据库"""
    # KEGG数据库配置（真实API）
    kegg_rest_url: str = "https://rest.kegg.jp"
    kegg_timeout: int = 30
    kegg_retry_attempts: int = 3
    
    # GO数据库配置（真实API）
    go_api_url: str = "http://api.geneontology.org"
    go_obo_url: str = "http://purl.obolibrary.org/obo/go.obo"
    
    # eggNOG数据库配置
    eggnog_data_dir: str = "/data/eggnog_data"  # 本地eggNOG数据目录
    eggnog_db_version: str = "5.0.2"
    
    # BiGG Models数据库配置（真实API）
    bigg_api_url: str = "http://bigg.ucsd.edu/api/v2"
    bigg_static_url: str = "http://bigg.ucsd.edu/static/models"
    
    # Pfam数据库配置
    pfam_api_url: str = "https://pfam.xfam.org/search"
    
    # COG数据库配置
    cog_data_url: str = "https://ftp.ncbi.nlm.nih.gov/pub/COG/COG2020/data"


@dataclass 
class VCFFilterConfig:
    """VCF文件过滤配置 - 基于SnpEFF注释的真实效应类型"""
    # 非同义突变效应类型（基于SnpEFF官方文档）
    non_synonymous_effects: List[str] = field(default_factory=lambda: [
        'missense_variant',           # 错义突变：氨基酸改变
        'stop_gained',               # 终止密码子获得：产生提前终止
        'stop_lost',                 # 终止密码子丢失：延长蛋白质
        'start_lost',                # 起始密码子丢失：影响翻译起始
        'frameshift_variant',        # 移码突变：改变阅读框
        'inframe_insertion',         # 框内插入：不改变阅读框的插入
        'inframe_deletion',          # 框内缺失：不改变阅读框的缺失
        'protein_altering_variant',  # 蛋白质改变突变
        'disruptive_inframe_insertion',  # 破坏性框内插入
        'disruptive_inframe_deletion'    # 破坏性框内缺失
    ])
    
    # 影响等级过滤（基于SnpEFF分类）
    impact_levels: List[str] = field(default_factory=lambda: ['HIGH', 'MODERATE'])
    
    # 质量控制参数
    min_quality: float = 30.0        # 最小质量分数（PHRED）
    min_read_depth: int = 10         # 最小读取深度
    min_allele_frequency: float = 0.0  # 最小等位基因频率（纯培养物可为0）
    max_allele_frequency: float = 1.0  # 最大等位基因频率
    
    # 高级过滤参数
    exclude_intergenic: bool = True   # 排除基因间区突变
    exclude_intronic: bool = True     # 排除内含子突变
    require_coding_sequence: bool = True  # 要求在编码序列中


@dataclass
class MetabolicModelConfig:
    """代谢模型分析配置 - 基于真实的约束代谢模型参数"""
    # FBA分析参数
    fba_tolerance: float = 1e-9      # 数值容差
    fba_solver: str = 'auto'         # 求解器选择（auto自动选择最佳）
    
    # 必需基因判断阈值
    essential_gene_threshold: float = 0.01  # 生长率低于此比例视为必需基因
    
    # 通量变化分析参数
    flux_variability: bool = True     # 是否进行FVA分析
    perform_fva: bool = True         # 执行通量变化分析
    fva_fraction: float = 0.95       # FVA分析的最优解比例
    
    # 梭菌特异性目标代谢产物交换反应ID（基于真实代谢模型）
    target_metabolites: Dict[str, str] = field(default_factory=lambda: {
        'ethanol': 'EX_etoh_e',          # 乙醇
        'acetate': 'EX_ac_e',            # 乙酸
        'butyrate': 'EX_but_e',          # 丁酸
        'butanol': 'EX_btoh_e',          # 丁醇
        'acetone': 'EX_actn_e',          # 丙酮
        '2_3_butanediol': 'EX_btd_RR_e', # 2,3-丁二醇
        'lactate': 'EX_lac__D_e',        # D-乳酸
        'formate': 'EX_for_e',           # 甲酸
        'hydrogen': 'EX_h2_e',           # 氢气
        'co2': 'EX_co2_e',               # 二氧化碳
        'co': 'EX_co_e'                  # 一氧化碳
    })
    
    # Wood-Ljungdahl通路关键反应（基于真实代谢网络重建）
    wlp_reactions: List[str] = field(default_factory=lambda: [
        'FDH7',      # 甲酸脱氢酶
        'FTHFD',     # 甲酰基-THF脱氢酶
        'MTHFC',     # 亚甲基-THF环化酶
        'MTHFD',     # 亚甲基-THF脱氢酶
        'MTHFR5',    # 亚甲基-THF还原酶
        'CODH_ACS',  # CO脱氢酶/乙酰辅酶A合成酶复合体
        'PTAr',      # 磷酸转乙酰酶（可逆）
        'ACKr',      # 乙酸激酶（可逆）
        'ACS',       # 乙酰辅酶A合成酶
        'AOR1',      # 醛:铁氧还蛋白氧化还原酶1
        'AOR2'       # 醛:铁氧还蛋白氧化还原酶2
    ])
    
    # 电子传递和能量守恒反应（梭菌特异性）
    electron_transfer_reactions: List[str] = field(default_factory=lambda: [
        'RNF',       # Rnf复合体（能量守恒）
        'NFN',       # Nfn复合体（NADH-铁氧还蛋白氧化还原酶）
        'HYD1',      # 氢化酶1
        'HYD2',      # 氢化酶2
        'FDH1',      # 甲酸脱氢酶1
        'FDH2',      # 甲酸脱氢酶2
        'ETF',       # 电子转移黄素蛋白
        'ETFQO',     # ETF-泛醌氧化还原酶
        'ATPS4r',    # ATP合酶
        'FRD2',      # 延胡索酸还原酶
        'SQR'        # 硫醌还原酶
    ])
    
    # 辅因子对（基于真实的生化反应）
    cofactor_pairs: Dict[str, List[str]] = field(default_factory=lambda: {
        'nad': ['nad_c', 'nadh_c'],           # NAD+/NADH
        'nadp': ['nadp_c', 'nadph_c'],        # NADP+/NADPH
        'fad': ['fad_c', 'fadh2_c'],          # FAD/FADH2
        'fdred': ['fdred_c', 'fdox_c'],       # 还原型/氧化型铁氧还蛋白
        'atp': ['atp_c', 'adp_c', 'amp_c'],   # ATP/ADP/AMP
        'coa': ['coa_c', 'accoa_c'],          # 辅酶A/乙酰辅酶A
        'thf': ['thf_c', 'mthf_c', 'fthf_c'], # 四氢叶酸衍生物
        'mqn': ['mqn7_c', 'mqn8_c']           # 甲萘醌氧化/还原态
    })
    
    # 培养基条件设置（单位：mmol/gDW/h，基于真实梭菌培养条件）
    medium_conditions: Dict[str, float] = field(default_factory=lambda: {
        # 碳源
        'EX_co2_e': -20.0,       # CO2（主要碳源）
        'EX_h2_e': -40.0,        # H2（电子供体）
        'EX_co_e': -10.0,        # CO（辅助碳源）
        'EX_for_e': -5.0,        # 甲酸（辅助碳源）
        
        # 基本营养素
        'EX_h2o_e': -1000.0,     # 水
        'EX_h_e': -1000.0,       # 质子
        'EX_nh4_e': -10.0,       # 氨（氮源）
        'EX_pi_e': -10.0,        # 磷酸盐
        'EX_so4_e': -5.0,        # 硫酸盐
        
        # 金属离子
        'EX_mg2_e': -1.0,        # 镁离子
        'EX_fe2_e': -0.1,        # 铁离子
        'EX_k_e': -1.0,          # 钾离子
        'EX_ca2_e': -0.1,        # 钙离子
        'EX_zn2_e': -0.01,       # 锌离子
        'EX_mn2_e': -0.01,       # 锰离子
        'EX_ni2_e': -0.01,       # 镍离子（氢化酶必需）
        'EX_co2_e': -0.001,      # 钴离子（B12合成）
        
        # 维生素和辅因子
        'EX_btn_e': -0.001,      # 生物素
        'EX_cbl1_e': -0.001,     # 维生素B12
        'EX_pnto__R_e': -0.001,  # 泛酸
        'EX_pydam_e': -0.001     # 吡哆醛
    })
    
    # 模型质量控制参数
    gap_fill: bool = True            # 是否进行gap-filling
    biomass_reaction: str = 'BIOMASS_Reaction'  # 生物量反应ID
    objective_direction: str = 'max'  # 优化方向


@dataclass
class PathwayAnalysisConfig:
    """通路分析配置 - 基于真实数据库的通路定义"""
    # 使用的真实数据库
    databases: List[str] = field(default_factory=lambda: [
        "KEGG",      # KEGG PATHWAY数据库
        "GO",        # Gene Ontology
        "COG",       # COG功能分类
        "eggNOG",    # eggNOG功能分类
        "Pfam"       # Pfam蛋白家族
    ])
    
    # 统计分析参数
    significance_threshold: float = 0.05    # 显著性阈值
    fdr_method: str = "fdr_bh"             # 多重检验校正方法
    min_gene_set_size: int = 3             # 最小基因集大小
    max_gene_set_size: int = 500           # 最大基因集大小
    permutation_num: int = 1000            # 置换检验次数
    confidence_level: float = 0.95         # 置信区间水平
    
    # 梭菌特异性通路定义（基于文献和数据库）
    custom_pathways: Dict[str, Dict[str, Any]] = field(default_factory=lambda: {
        'wood_ljungdahl_pathway': {
            'name': 'Wood-Ljungdahl pathway',
            'description': '梭菌CO2固定的主要通路',
            'genes': [],  # 将从真实注释数据中获取
            'ko_terms': ['K00169', 'K00194', 'K00556', 'K00297', 'K00288'],
            'go_terms': ['GO:0019395', 'GO:0070189']
        },
        'rnf_complex': {
            'name': 'Rnf complex pathway',
            'description': '能量守恒复合体',
            'genes': [],
            'ko_terms': ['K03614', 'K03615', 'K03616', 'K03617'],
            'go_terms': ['GO:0009055', 'GO:0016651']
        },
        'butyrate_biosynthesis': {
            'name': 'Butyrate biosynthesis',
            'description': '丁酸合成通路',
            'genes': [],
            'ko_terms': ['K00626', 'K01034', 'K00929'],
            'go_terms': ['GO:0006085']
        },
        'ethanol_biosynthesis': {
            'name': 'Ethanol biosynthesis',
            'description': '乙醇合成通路',
            'genes': [],
            'ko_terms': ['K04072', 'K00001'],
            'go_terms': ['GO:0006069']
        }
    })


@dataclass
class InputFiles:
    """输入文件配置"""
    vcf_file: str = ""                    # VCF文件路径
    eggnog_annotations: str = ""          # eggNOG注释文件
    gff_file: str = ""                   # 基因注释文件（可选）
    model_file: str = ""                 # 代谢模型文件（可选）
    reference_genome: str = ""           # 参考基因组文件（可选）
    protein_sequences: str = ""          # 蛋白质序列文件（可选）


@dataclass
class OutputConfig:
    """输出配置"""
    results_dir: str = "results"         # 结果输出目录
    save_intermediate_files: bool = True  # 是否保存中间文件
    generate_plots: bool = True          # 是否生成图表
    plot_format: List[str] = field(default_factory=lambda: ['png', 'pdf'])
    create_html_report: bool = True      # 是否创建HTML报告
    compress_results: bool = False       # 是否压缩结果文件
    
    # 子目录结构
    subdirs: Dict[str, str] = field(default_factory=lambda: {
        'mutations': 'mutations',
        'annotations': 'annotations', 
        'metabolic': 'metabolic_analysis',
        'enrichment': 'enrichment_analysis',
        'visualizations': 'visualizations',
        'quality_control': 'quality_control',
        'logs': 'logs'
    })


@dataclass
class ComputingConfig:
    """计算配置"""
    n_jobs: int = -1                     # 并行进程数（-1使用所有CPU）
    memory_limit_gb: int = 8             # 内存限制（GB）
    temp_dir: str = "/tmp"               # 临时文件目录
    cache_enabled: bool = True           # 是否启用缓存
    cache_size_gb: int = 2               # 缓存大小限制
    
    # 网络设置
    request_timeout: int = 30            # 网络请求超时时间
    max_retries: int = 3                 # 最大重试次数
    retry_delay: float = 1.0             # 重试延迟（秒）


class ConfigManager:
    """配置管理器 - 管理所有配置参数，确保使用真实数据源"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        参数:
            config_file: 配置文件路径（YAML格式）
        """
        # 初始化所有配置部分
        self.database = DatabaseConfig()
        self.vcf_filter = VCFFilterConfig()
        self.metabolic_model = MetabolicModelConfig()
        self.pathway_analysis = PathwayAnalysisConfig()
        self.input_files = InputFiles()
        self.output = OutputConfig()
        self.computing = ComputingConfig()
        
        # 项目信息
        self.project_info = {
            'name': 'clostridium_analysis',
            'version': '3.0.0',
            'organism': 'Clostridium ljungdahlii',
            'strain': 'DSM_13528',
            'description': '梭菌突变分析工作流程'
        }
        
        # 如果提供了配置文件，则加载
        if config_file:
            self.load_config(config_file)
        
        # 验证数据库连接
        self._verify_database_connections()
        
        # 设置日志
        self._setup_logging()
        
        logger.info("配置管理器初始化完成")
    
    def load_config(self, config_file: str) -> None:
        """
        从YAML文件加载配置
        
        参数:
            config_file: 配置文件路径
        """
        config_path = Path(config_file)
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_file}")
        
        logger.info(f"加载配置文件: {config_file}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        # 更新各个配置部分
        if 'database' in config_data:
            self._update_dataclass(self.database, config_data['database'])
        
        if 'vcf_filter' in config_data:
            self._update_dataclass(self.vcf_filter, config_data['vcf_filter'])
        
        if 'metabolic_model' in config_data:
            self._update_dataclass(self.metabolic_model, config_data['metabolic_model'])
        
        if 'pathway_analysis' in config_data:
            self._update_dataclass(self.pathway_analysis, config_data['pathway_analysis'])
        
        if 'input_files' in config_data:
            self._update_dataclass(self.input_files, config_data['input_files'])
        
        if 'output' in config_data:
            self._update_dataclass(self.output, config_data['output'])
        
        if 'computing' in config_data:
            self._update_dataclass(self.computing, config_data['computing'])
        
        if 'project' in config_data:
            self.project_info.update(config_data['project'])
        
        logger.info("配置文件加载完成")
    
    def _verify_database_connections(self) -> None:
        """验证数据库连接的可用性"""
        logger.info("验证数据库连接...")
        
        # 验证KEGG REST API
        try:
            response = requests.get(
                f"{self.database.kegg_rest_url}/info/kegg",
                timeout=self.database.kegg_timeout
            )
            if response.status_code == 200:
                logger.info("✓ KEGG数据库连接正常")
            else:
                logger.warning(f"⚠ KEGG数据库连接异常: {response.status_code}")
        except Exception as e:
            logger.warning(f"⚠ KEGG数据库连接失败: {e}")
        
        # 验证GO API
        try:
            response = requests.get(
                f"{self.database.go_api_url}/api/bioentity/gene/NCBIGene:1",
                timeout=10
            )
            if response.status_code in [200, 404]:  # 404也算正常，说明API可达
                logger.info("✓ GO数据库连接正常")
            else:
                logger.warning(f"⚠ GO数据库连接异常: {response.status_code}")
        except Exception as e:
            logger.warning(f"⚠ GO数据库连接失败: {e}")
        
        # 验证BiGG Models API
        try:
            response = requests.get(
                f"{self.database.bigg_api_url}/models",
                timeout=10
            )
            if response.status_code == 200:
                logger.info("✓ BiGG Models数据库连接正常")
            else:
                logger.warning(f"⚠ BiGG Models数据库连接异常: {response.status_code}")
        except Exception as e:
            logger.warning(f"⚠ BiGG Models数据库连接失败: {e}")
    
    def save_config(self, output_file: str) -> None:
        """
        保存当前配置到YAML文件
        
        参数:
            output_file: 输出文件路径
        """
        config_data = {
            'project': self.project_info,
            'database': asdict(self.database),
            'vcf_filter': asdict(self.vcf_filter),
            'metabolic_model': asdict(self.metabolic_model),
            'pathway_analysis': asdict(self.pathway_analysis),
            'input_files': asdict(self.input_files),
            'output': asdict(self.output),
            'computing': asdict(self.computing)
        }
        
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True, sort_keys=False)
        
        logger.info(f"配置已保存到: {output_file}")
    
    def validate_config(self) -> List[str]:
        """
        验证配置的完整性和合理性
        
        返回:
            错误信息列表
        """
        errors = []
        
        # 检查必需的输入文件
        required_files = ['vcf_file', 'eggnog_annotations']
        for file_type in required_files:
            file_path = getattr(self.input_files, file_type)
            if not file_path:
                errors.append(f"缺少必需的输入文件: {file_type}")
            elif not Path(file_path).exists():
                errors.append(f"输入文件不存在: {file_type} = {file_path}")
        
        # 检查数值参数的合理性
        if self.vcf_filter.min_quality < 0:
            errors.append("VCF质量阈值不能为负数")
        
        if self.metabolic_model.fba_tolerance <= 0:
            errors.append("FBA容差必须大于0")
        
        if not (0 < self.pathway_analysis.significance_threshold <= 1):
            errors.append("显著性阈值必须在0和1之间")
        
        if self.computing.n_jobs < -1 or self.computing.n_jobs == 0:
            errors.append("并行进程数必须为-1或正整数")
        
        # 检查输出目录是否可写
        output_dir = Path(self.output.results_dir)
        try:
            output_dir.mkdir(parents=True, exist_ok=True)
            test_file = output_dir / '.test_write'
            test_file.touch()
            test_file.unlink()
        except Exception as e:
            errors.append(f"输出目录不可写: {self.output.results_dir} - {e}")
        
        # 验证数据库URL格式
        database_urls = {
            'KEGG REST': self.database.kegg_rest_url,
            'GO API': self.database.go_api_url,
            'BiGG API': self.database.bigg_api_url
        }
        
        for name, url in database_urls.items():
            try:
                result = urllib.parse.urlparse(url)
                if not all([result.scheme, result.netloc]):
                    errors.append(f"{name} URL格式无效: {url}")
            except Exception:
                errors.append(f"{name} URL格式无效: {url}")
        
        return errors
    
    def get_output_path(self, subdir: str, filename: str) -> Path:
        """
        获取输出文件的完整路径
        
        参数:
            subdir: 子目录名
            filename: 文件名
            
        返回:
            完整路径
        """
        base_dir = Path(self.output.results_dir)
        subdir_name = self.output.subdirs.get(subdir, subdir)
        output_path = base_dir / subdir_name / filename
        output_path.parent.mkdir(parents=True, exist_ok=True)
        return output_path
    
    def _update_dataclass(self, obj: Any, data: Dict[str, Any]) -> None:
        """
        更新数据类对象的字段
        
        参数:
            obj: 数据类对象
            data: 更新数据
        """
        for key, value in data.items():
            if hasattr(obj, key):
                current_value = getattr(obj, key)
                if isinstance(current_value, dict) and isinstance(value, dict):
                    # 合并字典
                    current_value.update(value)
                else:
                    setattr(obj, key, value)
    
    def _setup_logging(self) -> None:
        """设置日志系统"""
        log_dir = self.get_output_path('logs', '')
        log_file = log_dir / f'workflow_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
        
        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        logger.info(f"日志文件: {log_file}")
    
    @property
    def input_files_dict(self) -> Dict[str, str]:
        """获取输入文件字典"""
        return asdict(self.input_files)


# 便捷函数
def load_config(config_file: str) -> ConfigManager:
    """
    加载配置文件的便捷函数
    
    参数:
        config_file: 配置文件路径
        
    返回:
        配置管理器实例
    """
    return ConfigManager(config_file)


def create_default_config(output_file: str = "config/default_config.yaml") -> None:
    """
    创建默认配置文件
    
    参数:
        output_file: 输出文件路径
    """
    config = ConfigManager()
    config.save_config(output_file)
    print(f"默认配置文件已创建: {output_file}")


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='配置管理器')
    parser.add_argument('--create-default', action='store_true',
                       help='创建默认配置文件')
    parser.add_argument('--config', default='config/default_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--validate', action='store_true',
                       help='验证配置文件')
    
    args = parser.parse_args()
    
    if args.create_default:
        create_default_config(args.config)
    elif args.validate:
        try:
            config = ConfigManager(args.config)
            errors = config.validate_config()
            if errors:
                print("配置验证失败:")
                for error in errors:
                    print(f"  - {error}")
            else:
                print("配置验证通过!")
        except Exception as e:
            print(f"配置加载失败: {e}")
    else:
        print("请使用 --create-default 创建默认配置或 --validate 验证配置")