#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注释整合器模块 - 梭菌生物信息学工作流程
作者：生物信息学分析团队
版本：3.0.0
描述：整合多个真实数据库的功能注释，包括eggNOG、KEGG、GO、COG和Pfam
     所有数据来源均为真实的生物信息学数据库API或数据文件
"""

import logging
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field, asdict
from collections import defaultdict
import json
import requests
import time
import re
import gzip
from urllib.parse import urljoin, quote
import sqlite3
from Bio import SeqIO
from io import StringIO

# 导入其他模块
from config_manager import ConfigManager

logger = logging.getLogger(__name__)


@dataclass
class GeneAnnotation:
    """基因注释数据结构 - 整合多个真实数据库的注释信息"""
    gene_id: str                           # 基因ID
    gene_name: str = ''                    # 基因名称
    
    # eggNOG注释（真实数据）
    eggnog_ogs: List[str] = field(default_factory=list)      # eggNOG正交基因群
    eggnog_description: str = ''           # 功能描述
    
    # KEGG注释（真实API数据）
    kegg_kos: List[str] = field(default_factory=list)        # KEGG KO号
    kegg_pathways: List[str] = field(default_factory=list)   # KEGG通路
    kegg_modules: List[str] = field(default_factory=list)    # KEGG模块
    
    # GO注释（真实数据）
    go_terms: List[str] = field(default_factory=list)        # GO术语
    go_functions: List[str] = field(default_factory=list)    # 分子功能
    go_processes: List[str] = field(default_factory=list)    # 生物过程
    go_components: List[str] = field(default_factory=list)   # 细胞组分
    
    # COG注释（真实数据）
    cog_categories: List[str] = field(default_factory=list)  # COG功能分类
    cog_descriptions: List[str] = field(default_factory=list) # COG描述
    
    # Pfam注释（真实数据）
    pfam_domains: List[str] = field(default_factory=list)    # Pfam蛋白域
    pfam_descriptions: List[str] = field(default_factory=list) # Pfam描述
    
    # 酶分类（真实EC数据）
    ec_numbers: List[str] = field(default_factory=list)      # EC号
    
    # 序列信息
    protein_length: int = 0                # 蛋白质长度
    protein_sequence: str = ''             # 蛋白质序列（可选）
    
    # 置信度和质量
    annotation_score: float = 1.0          # 注释质量评分
    evidence_codes: List[str] = field(default_factory=list)  # 证据代码
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


@dataclass
class PathwayInfo:
    """通路信息数据结构 - 基于真实数据库的通路定义"""
    pathway_id: str                        # 通路ID
    pathway_name: str                      # 通路名称
    database: str                          # 数据库来源（KEGG, GO, COG等）
    genes: Set[str] = field(default_factory=set)  # 包含的基因
    description: str = ''                  # 通路描述
    category: str = ''                     # 通路分类
    
    # 附加信息
    pathway_class: str = ''                # 通路类别
    organism_specific: bool = False        # 是否为物种特异性
    confidence_level: str = 'high'        # 置信度等级
    
    def add_gene(self, gene_id: str) -> None:
        """添加基因到通路"""
        self.genes.add(gene_id)
    
    def gene_count(self) -> int:
        """获取基因数量"""
        return len(self.genes)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        result['genes'] = list(self.genes)  # 转换Set为List
        return result


class KEGGClient:
    """KEGG数据库客户端 - 连接真实的KEGG REST API"""
    
    def __init__(self, base_url: str = "https://rest.kegg.jp"):
        """
        初始化KEGG客户端
        
        参数:
            base_url: KEGG REST API基础URL
        """
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Clostridium-Analysis-Pipeline/3.0.0'
        })
        
        # 缓存
        self.pathway_cache = {}
        self.ko_cache = {}
        
        logger.info(f"KEGG客户端初始化完成: {self.base_url}")
    
    def get_ko_info(self, ko_id: str) -> Optional[Dict[str, Any]]:
        """
        获取KO（KEGG Orthology）信息
        
        参数:
            ko_id: KO编号（如K00001）
            
        返回:
            KO信息字典
        """
        if ko_id in self.ko_cache:
            return self.ko_cache[ko_id]
        
        try:
            # 确保KO ID格式正确
            if not ko_id.startswith('K'):
                ko_id = f'K{ko_id}'
            
            url = f"{self.base_url}/get/{ko_id}"
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                ko_info = self._parse_kegg_entry(response.text)
                ko_info['ko_id'] = ko_id
                self.ko_cache[ko_id] = ko_info
                return ko_info
            else:
                logger.warning(f"KEGG KO {ko_id} 未找到")
                return None
                
        except Exception as e:
            logger.error(f"获取KEGG KO信息失败 {ko_id}: {e}")
            return None
    
    def get_pathway_info(self, pathway_id: str) -> Optional[Dict[str, Any]]:
        """
        获取KEGG通路信息
        
        参数:
            pathway_id: 通路ID（如map00010）
            
        返回:
            通路信息字典
        """
        if pathway_id in self.pathway_cache:
            return self.pathway_cache[pathway_id]
        
        try:
            url = f"{self.base_url}/get/{pathway_id}"
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                pathway_info = self._parse_kegg_entry(response.text)
                pathway_info['pathway_id'] = pathway_id
                self.pathway_cache[pathway_id] = pathway_info
                return pathway_info
            else:
                logger.warning(f"KEGG通路 {pathway_id} 未找到")
                return None
                
        except Exception as e:
            logger.error(f"获取KEGG通路信息失败 {pathway_id}: {e}")
            return None
    
    def get_organism_pathways(self, organism_code: str = "clo") -> List[Dict[str, Any]]:
        """
        获取特定物种的通路列表
        
        参数:
            organism_code: 物种代码（如clo代表梭菌）
            
        返回:
            通路信息列表
        """
        try:
            url = f"{self.base_url}/list/pathway/{organism_code}"
            response = self.session.get(url, timeout=30)
            
            if response.status_code == 200:
                pathways = []
                for line in response.text.strip().split('\n'):
                    if line.strip():
                        parts = line.split('\t')
                        if len(parts) >= 2:
                            pathway_id = parts[0]
                            pathway_name = parts[1]
                            pathways.append({
                                'pathway_id': pathway_id,
                                'pathway_name': pathway_name,
                                'organism': organism_code
                            })
                
                logger.info(f"获取到 {len(pathways)} 个 {organism_code} 物种通路")
                return pathways
            else:
                logger.warning(f"获取物种通路失败: {organism_code}")
                return []
                
        except Exception as e:
            logger.error(f"获取物种通路失败: {e}")
            return []
    
    def _parse_kegg_entry(self, entry_text: str) -> Dict[str, Any]:
        """解析KEGG条目文本"""
        info = {}
        current_section = None
        current_content = []
        
        for line in entry_text.split('\n'):
            if line.startswith('///'):
                break
            
            if line and not line.startswith(' '):
                # 保存前一个部分
                if current_section and current_content:
                    info[current_section] = '\n'.join(current_content).strip()
                
                # 开始新部分
                if ' ' in line:
                    current_section, content = line.split(' ', 1)
                    current_content = [content.strip()]
                else:
                    current_section = line.strip()
                    current_content = []
            elif current_section and line.startswith(' '):
                # 继续当前部分
                current_content.append(line.strip())
        
        # 保存最后一个部分
        if current_section and current_content:
            info[current_section] = '\n'.join(current_content).strip()
        
        return info
    
    def batch_get_ko_info(self, ko_list: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        批量获取KO信息
        
        参数:
            ko_list: KO编号列表
            
        返回:
            KO信息字典
        """
        ko_info_dict = {}
        
        for ko_id in ko_list:
            ko_info = self.get_ko_info(ko_id)
            if ko_info:
                ko_info_dict[ko_id] = ko_info
            
            # 避免过于频繁的请求
            time.sleep(0.1)
        
        return ko_info_dict


class GOClient:
    """Gene Ontology数据库客户端 - 连接真实的GO API"""
    
    def __init__(self, api_url: str = "http://api.geneontology.org", 
                 obo_url: str = "http://purl.obolibrary.org/obo/go.obo"):
        """
        初始化GO客户端
        
        参数:
            api_url: GO API基础URL
            obo_url: GO OBO文件URL
        """
        self.api_url = api_url
        self.obo_url = obo_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Clostridium-Analysis-Pipeline/3.0.0',
            'Accept': 'application/json'
        })
        
        # 缓存
        self.term_cache = {}
        self.go_hierarchy = {}
        
        logger.info(f"GO客户端初始化完成: {self.api_url}")
    
    def get_term_info(self, go_id: str) -> Optional[Dict[str, Any]]:
        """
        获取GO术语信息
        
        参数:
            go_id: GO术语ID（如GO:0008150）
            
        返回:
            GO术语信息
        """
        if go_id in self.term_cache:
            return self.term_cache[go_id]
        
        try:
            # 确保GO ID格式正确
            if not go_id.startswith('GO:'):
                go_id = f'GO:{go_id.zfill(7)}'
            
            url = f"{self.api_url}/api/ontology/term/{go_id}"
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                term_info = response.json()
                self.term_cache[go_id] = term_info
                return term_info
            else:
                logger.warning(f"GO术语 {go_id} 未找到")
                return None
                
        except Exception as e:
            logger.debug(f"获取GO术语信息失败 {go_id}: {e}")
            return None
    
    def classify_go_terms(self, go_terms: List[str]) -> Dict[str, List[str]]:
        """
        按GO分类对GO术语进行分类
        
        参数:
            go_terms: GO术语ID列表
            
        返回:
            分类后的GO术语字典
        """
        classified = {
            'molecular_function': [],
            'biological_process': [],
            'cellular_component': []
        }
        
        for go_id in go_terms:
            term_info = self.get_term_info(go_id)
            if term_info:
                namespace = term_info.get('namespace', '')
                if 'molecular_function' in namespace:
                    classified['molecular_function'].append(go_id)
                elif 'biological_process' in namespace:
                    classified['biological_process'].append(go_id)
                elif 'cellular_component' in namespace:
                    classified['cellular_component'].append(go_id)
            
            # 避免过于频繁的请求
            time.sleep(0.05)
        
        return classified


class AnnotationIntegrator:
    """注释整合器类 - 整合多个真实数据库的功能注释"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化注释整合器
        
        参数:
            config: 配置管理器实例
        """
        self.config = config
        self.gene_annotations: Dict[str, GeneAnnotation] = {}
        self.pathways: Dict[str, PathwayInfo] = {}
        
        # 数据库客户端
        self.kegg_client = KEGGClient(config.database.kegg_rest_url)
        self.go_client = GOClient(config.database.go_api_url, config.database.go_obo_url)
        
        # 功能术语到基因的映射
        self.go_to_genes: Dict[str, Set[str]] = defaultdict(set)
        self.kegg_to_genes: Dict[str, Set[str]] = defaultdict(set)
        self.pfam_to_genes: Dict[str, Set[str]] = defaultdict(set)
        self.cog_to_genes: Dict[str, Set[str]] = defaultdict(set)
        
        # 术语描述缓存
        self.term_descriptions: Dict[str, str] = {}
        
        logger.info("注释整合器初始化完成")
    
    def load_eggnog_annotations(self, annotation_file: Optional[str] = None) -> None:
        """
        加载eggnog-mapper注释文件（真实的eggNOG数据库注释）
        
        参数:
            annotation_file: 注释文件路径
        """
        if annotation_file is None:
            annotation_file = self.config.input_files.eggnog_annotations
        
        if not annotation_file or not Path(annotation_file).exists():
            raise FileNotFoundError(f"eggNOG注释文件不存在: {annotation_file}")
        
        logger.info(f"加载eggNOG注释文件: {annotation_file}")
        
        # 清空现有注释
        self.gene_annotations.clear()
        
        # 读取注释文件
        try:
            # eggNOG-mapper输出格式解析
            df = pd.read_csv(annotation_file, sep='\t', comment='#', low_memory=False)
            logger.info(f"读取到 {len(df)} 条注释记录")
            
            # 标准字段映射（基于eggNOG-mapper v2.1.13标准输出）
            field_mapping = {
                'query': ['query_name', 'query', '#query'],
                'eggnog_ogs': ['eggNOG_OGs', 'eggnog_og', 'ogs'],
                'max_annot_lvl': ['max_annot_lvl', 'taxonomic_level'],
                'cog_category': ['COG_category', 'cog_cat', 'cog_category'],
                'description': ['Description', 'description', 'desc'],
                'preferred_name': ['Preferred_name', 'preferred_name', 'gene_name'],
                'go_terms': ['GOs', 'go_terms', 'go'],
                'ec': ['EC', 'ec', 'ec_number'],
                'kegg_ko': ['KEGG_ko', 'kegg_ko', 'ko'],
                'kegg_pathway': ['KEGG_Pathway', 'kegg_pathway', 'pathway'],
                'kegg_module': ['KEGG_Module', 'kegg_module', 'module'],
                'pfam': ['PFAMs', 'pfam', 'pfam_domains']
            }
            
            # 自动检测字段名
            detected_fields = {}
            for standard_field, possible_names in field_mapping.items():
                for name in possible_names:
                    if name in df.columns:
                        detected_fields[standard_field] = name
                        break
            
            logger.info(f"检测到的字段映射: {detected_fields}")
            
            # 处理每条注释记录
            processed_count = 0
            for idx, row in df.iterrows():
                try:
                    # 获取基因ID
                    gene_id = str(row[detected_fields.get('query', df.columns[0])]).strip()
                    if not gene_id or gene_id == 'nan':
                        continue
                    
                    # 创建基因注释对象
                    annotation = GeneAnnotation(gene_id=gene_id)
                    
                    # 基因名称
                    if 'preferred_name' in detected_fields:
                        gene_name = str(row[detected_fields['preferred_name']])
                        if gene_name and gene_name != 'nan':
                            annotation.gene_name = gene_name
                    
                    # eggNOG OGs
                    if 'eggnog_ogs' in detected_fields:
                        ogs = str(row[detected_fields['eggnog_ogs']])
                        if ogs and ogs != 'nan':
                            annotation.eggnog_ogs = [og.strip() for og in ogs.split(',')]
                    
                    # 功能描述
                    if 'description' in detected_fields:
                        desc = str(row[detected_fields['description']])
                        if desc and desc != 'nan':
                            annotation.eggnog_description = desc
                    
                    # COG分类
                    if 'cog_category' in detected_fields:
                        cog_cat = str(row[detected_fields['cog_category']])
                        if cog_cat and cog_cat != 'nan':
                            annotation.cog_categories = list(cog_cat)
                    
                    # GO术语
                    if 'go_terms' in detected_fields:
                        go_terms = str(row[detected_fields['go_terms']])
                        if go_terms and go_terms != 'nan':
                            go_list = [go.strip() for go in go_terms.split(',')]
                            annotation.go_terms = go_list
                            
                            # 分类GO术语
                            go_classified = self.go_client.classify_go_terms(go_list)
                            annotation.go_functions = go_classified['molecular_function']
                            annotation.go_processes = go_classified['biological_process']
                            annotation.go_components = go_classified['cellular_component']
                    
                    # EC号
                    if 'ec' in detected_fields:
                        ec_numbers = str(row[detected_fields['ec']])
                        if ec_numbers and ec_numbers != 'nan':
                            annotation.ec_numbers = [ec.strip() for ec in ec_numbers.split(',')]
                    
                    # KEGG KO
                    if 'kegg_ko' in detected_fields:
                        ko_terms = str(row[detected_fields['kegg_ko']])
                        if ko_terms and ko_terms != 'nan':
                            ko_list = [ko.strip() for ko in ko_terms.split(',')]
                            annotation.kegg_kos = ko_list
                            
                            # 获取KO详细信息
                            for ko in ko_list:
                                ko_info = self.kegg_client.get_ko_info(ko)
                                if ko_info:
                                    # 提取通路信息
                                    pathway_text = ko_info.get('PATHWAY', '')
                                    if pathway_text:
                                        pathways = re.findall(r'map\d+', pathway_text)
                                        annotation.kegg_pathways.extend(pathways)
                    
                    # KEGG通路
                    if 'kegg_pathway' in detected_fields:
                        pathways = str(row[detected_fields['kegg_pathway']])
                        if pathways and pathways != 'nan':
                            pathway_list = [p.strip() for p in pathways.split(',')]
                            annotation.kegg_pathways.extend(pathway_list)
                    
                    # KEGG模块
                    if 'kegg_module' in detected_fields:
                        modules = str(row[detected_fields['kegg_module']])
                        if modules and modules != 'nan':
                            annotation.kegg_modules = [m.strip() for m in modules.split(',')]
                    
                    # Pfam域
                    if 'pfam' in detected_fields:
                        pfam_domains = str(row[detected_fields['pfam']])
                        if pfam_domains and pfam_domains != 'nan':
                            annotation.pfam_domains = [p.strip() for p in pfam_domains.split(',')]
                    
                    # 计算注释质量评分
                    annotation.annotation_score = self._calculate_annotation_score(annotation)
                    
                    # 保存注释
                    self.gene_annotations[gene_id] = annotation
                    processed_count += 1
                    
                    # 更新映射
                    self._update_term_mappings(annotation)
                    
                    # 定期输出进度
                    if processed_count % 1000 == 0:
                        logger.info(f"已处理 {processed_count} 个基因注释")
                
                except Exception as e:
                    logger.warning(f"处理注释记录失败 (行 {idx}): {e}")
                    continue
            
            logger.info(f"成功加载 {processed_count} 个基因的注释信息")
            
            # 构建通路信息
            self._build_pathway_info()
            
        except Exception as e:
            logger.error(f"加载eggNOG注释文件失败: {e}")
            raise
    
    def _calculate_annotation_score(self, annotation: GeneAnnotation) -> float:
        """计算注释质量评分"""
        score = 0.0
        
        # 基于不同数据库注释的数量和质量
        if annotation.eggnog_description:
            score += 0.2
        if annotation.kegg_kos:
            score += 0.2 * min(1.0, len(annotation.kegg_kos) / 3)
        if annotation.go_terms:
            score += 0.2 * min(1.0, len(annotation.go_terms) / 5)
        if annotation.pfam_domains:
            score += 0.2 * min(1.0, len(annotation.pfam_domains) / 2)
        if annotation.ec_numbers:
            score += 0.2 * min(1.0, len(annotation.ec_numbers) / 2)
        
        return min(1.0, score)
    
    def _update_term_mappings(self, annotation: GeneAnnotation) -> None:
        """更新术语到基因的映射"""
        gene_id = annotation.gene_id
        
        # GO术语映射
        for go_term in annotation.go_terms:
            self.go_to_genes[go_term].add(gene_id)
        
        # KEGG KO映射
        for ko in annotation.kegg_kos:
            self.kegg_to_genes[ko].add(gene_id)
        
        # Pfam域映射
        for pfam in annotation.pfam_domains:
            self.pfam_to_genes[pfam].add(gene_id)
        
        # COG分类映射
        for cog in annotation.cog_categories:
            self.cog_to_genes[cog].add(gene_id)
    
    def _build_pathway_info(self) -> None:
        """构建通路信息"""
        logger.info("构建通路信息...")
        
        # 构建KEGG通路
        kegg_pathways = set()
        for annotation in self.gene_annotations.values():
            kegg_pathways.update(annotation.kegg_pathways)
        
        for pathway_id in kegg_pathways:
            try:
                pathway_info = self.kegg_client.get_pathway_info(pathway_id)
                if pathway_info:
                    pathway = PathwayInfo(
                        pathway_id=pathway_id,
                        pathway_name=pathway_info.get('NAME', pathway_id),
                        database='KEGG',
                        description=pathway_info.get('DESCRIPTION', ''),
                        category=pathway_info.get('CLASS', '')
                    )
                    
                    # 添加基因
                    for gene_id, annotation in self.gene_annotations.items():
                        if pathway_id in annotation.kegg_pathways:
                            pathway.add_gene(gene_id)
                    
                    self.pathways[pathway_id] = pathway
                    
            except Exception as e:
                logger.warning(f"构建KEGG通路信息失败 {pathway_id}: {e}")
        
        # 构建GO通路（基于生物过程）
        go_processes = set()
        for annotation in self.gene_annotations.values():
            go_processes.update(annotation.go_processes)
        
        for go_id in go_processes:
            try:
                go_info = self.go_client.get_term_info(go_id)
                if go_info:
                    pathway = PathwayInfo(
                        pathway_id=go_id,
                        pathway_name=go_info.get('label', go_id),
                        database='GO',
                        description=go_info.get('definition', ''),
                        category='biological_process'
                    )
                    
                    # 添加基因
                    for gene_id, annotation in self.gene_annotations.items():
                        if go_id in annotation.go_processes:
                            pathway.add_gene(gene_id)
                    
                    self.pathways[go_id] = pathway
                    
            except Exception as e:
                logger.debug(f"构建GO通路信息失败 {go_id}: {e}")
        
        # 构建自定义梭菌特异性通路
        self._build_custom_clostridium_pathways()
        
        logger.info(f"构建了 {len(self.pathways)} 个通路信息")
    
    def _build_custom_clostridium_pathways(self) -> None:
        """构建梭菌特异性的自定义通路"""
        custom_pathways = self.config.pathway_analysis.custom_pathways
        
        for pathway_key, pathway_data in custom_pathways.items():
            try:
                pathway = PathwayInfo(
                    pathway_id=pathway_key,
                    pathway_name=pathway_data['name'],
                    database='Custom',
                    description=pathway_data['description'],
                    category='clostridium_specific',
                    organism_specific=True
                )
                
                # 基于KO术语添加基因
                ko_terms = pathway_data.get('ko_terms', [])
                for gene_id, annotation in self.gene_annotations.items():
                    if any(ko in annotation.kegg_kos for ko in ko_terms):
                        pathway.add_gene(gene_id)
                
                # 基于GO术语添加基因
                go_terms = pathway_data.get('go_terms', [])
                for gene_id, annotation in self.gene_annotations.items():
                    if any(go in annotation.go_terms for go in go_terms):
                        pathway.add_gene(gene_id)
                
                if pathway.gene_count() > 0:
                    self.pathways[pathway_key] = pathway
                    logger.info(f"构建自定义通路: {pathway.pathway_name} ({pathway.gene_count()} 个基因)")
                    
            except Exception as e:
                logger.warning(f"构建自定义通路失败 {pathway_key}: {e}")
    
    def get_gene_annotations(self, gene_ids: List[str]) -> Dict[str, GeneAnnotation]:
        """
        获取指定基因的注释信息
        
        参数:
            gene_ids: 基因ID列表
            
        返回:
            基因注释字典
        """
        annotations = {}
        
        for gene_id in gene_ids:
            if gene_id in self.gene_annotations:
                annotations[gene_id] = self.gene_annotations[gene_id]
            else:
                logger.debug(f"基因 {gene_id} 未找到注释信息")
        
        return annotations
    
    def get_pathway_genes(self, pathway_id: str) -> Set[str]:
        """
        获取通路中的基因列表
        
        参数:
            pathway_id: 通路ID
            
        返回:
            基因ID集合
        """
        if pathway_id in self.pathways:
            return self.pathways[pathway_id].genes
        else:
            return set()
    
    def search_genes_by_function(self, query: str) -> List[str]:
        """
        根据功能描述搜索基因
        
        参数:
            query: 搜索关键词
            
        返回:
            匹配的基因ID列表
        """
        matching_genes = []
        query_lower = query.lower()
        
        for gene_id, annotation in self.gene_annotations.items():
            # 搜索描述
            if query_lower in annotation.eggnog_description.lower():
                matching_genes.append(gene_id)
                continue
            
            # 搜索基因名称
            if query_lower in annotation.gene_name.lower():
                matching_genes.append(gene_id)
                continue
        
        return matching_genes
    
    def export_gene_sets(self, output_dir: Optional[str] = None) -> None:
        """
        导出基因集（GMT格式，用于富集分析）
        
        参数:
            output_dir: 输出目录
        """
        if output_dir is None:
            output_dir = self.config.get_output_path('annotations', '')
        else:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
        
        # 导出KEGG通路基因集
        kegg_gmt = output_dir / 'kegg_pathways.gmt'
        with open(kegg_gmt, 'w', encoding='utf-8') as f:
            for pathway_id, pathway in self.pathways.items():
                if pathway.database == 'KEGG' and pathway.gene_count() >= 3:
                    genes = '\t'.join(sorted(pathway.genes))
                    f.write(f"{pathway_id}\t{pathway.pathway_name}\t{genes}\n")
        
        logger.info(f"KEGG基因集已导出到: {kegg_gmt}")
        
        # 导出GO基因集
        go_gmt = output_dir / 'go_terms.gmt'
        with open(go_gmt, 'w', encoding='utf-8') as f:
            for pathway_id, pathway in self.pathways.items():
                if pathway.database == 'GO' and pathway.gene_count() >= 3:
                    genes = '\t'.join(sorted(pathway.genes))
                    f.write(f"{pathway_id}\t{pathway.pathway_name}\t{genes}\n")
        
        logger.info(f"GO基因集已导出到: {go_gmt}")
        
        # 导出自定义通路基因集
        custom_gmt = output_dir / 'custom_pathways.gmt'
        with open(custom_gmt, 'w', encoding='utf-8') as f:
            for pathway_id, pathway in self.pathways.items():
                if pathway.database == 'Custom' and pathway.gene_count() >= 2:
                    genes = '\t'.join(sorted(pathway.genes))
                    f.write(f"{pathway_id}\t{pathway.pathway_name}\t{genes}\n")
        
        logger.info(f"自定义通路基因集已导出到: {custom_gmt}")
    
    def save_annotations(self, output_dir: Optional[str] = None) -> None:
        """
        保存注释数据
        
        参数:
            output_dir: 输出目录
        """
        if output_dir is None:
            output_dir = self.config.get_output_path('annotations', '')
        else:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存基因注释表
        annotation_data = []
        for gene_id, annotation in self.gene_annotations.items():
            row = {
                'gene_id': gene_id,
                'gene_name': annotation.gene_name,
                'eggnog_description': annotation.eggnog_description,
                'kegg_kos': '; '.join(annotation.kegg_kos),
                'kegg_pathways': '; '.join(annotation.kegg_pathways),
                'go_terms': '; '.join(annotation.go_terms),
                'go_functions': '; '.join(annotation.go_functions),
                'go_processes': '; '.join(annotation.go_processes),
                'go_components': '; '.join(annotation.go_components),
                'cog_categories': '; '.join(annotation.cog_categories),
                'pfam_domains': '; '.join(annotation.pfam_domains),
                'ec_numbers': '; '.join(annotation.ec_numbers),
                'annotation_score': annotation.annotation_score
            }
            annotation_data.append(row)
        
        df = pd.DataFrame(annotation_data)
        
        # 保存为CSV
        annotation_file = output_dir / 'gene_annotations.csv'
        df.to_csv(annotation_file, index=False)
        logger.info(f"基因注释已保存到: {annotation_file}")
        
        # 保存为Excel
        excel_file = output_dir / 'gene_annotations.xlsx'
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Gene Annotations', index=False)
            
            # 通路信息
            pathway_data = []
            for pathway_id, pathway in self.pathways.items():
                pathway_data.append({
                    'pathway_id': pathway_id,
                    'pathway_name': pathway.pathway_name,
                    'database': pathway.database,
                    'gene_count': pathway.gene_count(),
                    'description': pathway.description,
                    'category': pathway.category
                })
            
            pathway_df = pd.DataFrame(pathway_data)
            pathway_df.to_excel(writer, sheet_name='Pathways', index=False)
        
        logger.info(f"Excel格式注释已保存到: {excel_file}")
        
        # 保存详细的JSON格式数据
        json_data = {
            'gene_annotations': {
                gene_id: ann.to_dict() 
                for gene_id, ann in self.gene_annotations.items()
            },
            'pathways': {
                pathway_id: pathway.to_dict()
                for pathway_id, pathway in self.pathways.items()
            },
            'statistics': {
                'total_genes': len(self.gene_annotations),
                'genes_with_go': len([g for g in self.gene_annotations.values() if g.go_terms]),
                'genes_with_kegg': len([g for g in self.gene_annotations.values() if g.kegg_kos]),
                'genes_with_pfam': len([g for g in self.gene_annotations.values() if g.pfam_domains]),
                'total_pathways': len(self.pathways),
                'pathway_distribution': {
                    db: len([p for p in self.pathways.values() if p.database == db])
                    for db in ['KEGG', 'GO', 'COG', 'Custom']
                }
            }
        }
        
        json_file = output_dir / 'annotation_data.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        logger.info(f"详细注释数据已保存到: {json_file}")


# 便捷函数
def load_annotations(annotation_file: str, config: ConfigManager) -> AnnotationIntegrator:
    """
    加载注释文件的便捷函数
    
    参数:
        annotation_file: 注释文件路径
        config: 配置管理器
        
    返回:
        注释整合器实例
    """
    integrator = AnnotationIntegrator(config)
    integrator.load_eggnog_annotations(annotation_file)
    return integrator


if __name__ == '__main__':
    # 测试代码
    import argparse
    from config_manager import ConfigManager
    
    parser = argparse.ArgumentParser(description='注释整合器')
    parser.add_argument('annotation_file', help='eggNOG注释文件路径')
    parser.add_argument('--config', default='config/default_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--export-gmt', action='store_true',
                       help='导出GMT格式基因集文件')
    parser.add_argument('--test-kegg', action='store_true',
                       help='测试KEGG连接')
    parser.add_argument('--test-go', action='store_true',
                       help='测试GO连接')
    
    args = parser.parse_args()
    
    # 加载配置
    config = ConfigManager(args.config)
    
    if args.test_kegg:
        # 测试KEGG连接
        kegg_client = KEGGClient()
        ko_info = kegg_client.get_ko_info('K00001')
        if ko_info:
            print("KEGG连接测试成功")
            print(f"示例KO信息: {ko_info.get('NAME', 'N/A')}")
        else:
            print("KEGG连接测试失败")
    elif args.test_go:
        # 测试GO连接
        go_client = GOClient()
        term_info = go_client.get_term_info('GO:0008150')
        if term_info:
            print("GO连接测试成功")
            print(f"示例GO术语: {term_info.get('label', 'N/A')}")
        else:
            print("GO连接测试失败")
    else:
        # 加载注释
        integrator = AnnotationIntegrator(config)
        integrator.load_eggnog_annotations(args.annotation_file)
        
        # 显示统计信息
        print(f"\n加载了 {len(integrator.gene_annotations)} 个基因的注释")
        print(f"构建了 {len(integrator.pathways)} 个通路/功能分类")
        
        # 显示数据库分布
        db_counts = defaultdict(int)
        for pathway in integrator.pathways.values():
            db_counts[pathway.database] += 1
        
        print("\n通路数据库分布:")
        for db, count in db_counts.items():
            print(f"  {db}: {count}")
        
        # 导出GMT文件
        if args.export_gmt:
            integrator.export_gene_sets()
            print("\nGMT文件已导出")
        
        # 保存注释数据
        integrator.save_annotations()
        print("\n注释数据已保存")