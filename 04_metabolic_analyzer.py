#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代谢模型分析模块 - 梭菌生物信息学工作流程
作者：生物信息学分析团队
版本：3.0.0
描述：整合基因组变异与代谢模型，预测突变对代谢网络和产物合成的影响
     完全基于真实的BiGG Models数据库和COBRApy生态系统
"""

import logging
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field, asdict
from collections import defaultdict
import json
import requests
import time
import tempfile
import os
import io
from urllib.parse import urljoin
import xml.etree.ElementTree as ET

# COBRApy相关导入 - 基于真实的约束代谢建模
import cobra
from cobra import Model, Reaction, Metabolite, Gene
from cobra.flux_analysis import flux_variability_analysis, single_gene_deletion
from cobra.flux_analysis import pfba, gapfill, phenotype_phase_plane
from cobra.sampling import sample
import cobra.io
from cobra.util import solver
from cobra.exceptions import Infeasible, OptimizationError

# 导入其他模块
from config_manager import ConfigManager
from vcf_processor import MutationRecord

logger = logging.getLogger(__name__)


@dataclass
class MetabolicImpact:
    """代谢影响数据结构 - 基于真实的代谢网络分析"""
    gene_id: str                           # 基因ID
    gene_name: str                         # 基因名称
    mutation_type: str                     # 突变类型组合
    impact_level: str                      # 影响等级（HIGH, MODERATE, LOW）
    
    # 反应影响（基于真实模型中的基因-反应关联）
    affected_reactions: List[str] = field(default_factory=list)  # 受影响的反应ID
    reaction_flux_changes: Dict[str, float] = field(default_factory=dict)  # 通量变化
    reaction_names: Dict[str, str] = field(default_factory=dict)  # 反应名称
    
    # 生长影响（基于FBA计算）
    growth_rate_change: float = 0.0        # 生长率变化（相对于野生型）
    is_essential: bool = False             # 是否为必需基因
    essentiality_score: float = 0.0       # 必需性评分
    
    # 代谢产物影响（基于交换反应分析）
    metabolite_changes: Dict[str, float] = field(default_factory=dict)  # 代谢产物浓度变化
    product_yield_changes: Dict[str, float] = field(default_factory=dict)  # 产物产量变化
    
    # 通路影响（基于反应注释）
    affected_pathways: List[str] = field(default_factory=list)  # 受影响的通路
    pathway_flux_changes: Dict[str, float] = field(default_factory=dict)  # 通路通量变化
    
    # 代谢网络属性
    subsystem_distribution: Dict[str, int] = field(default_factory=dict)  # 子系统分布
    centrality_measures: Dict[str, float] = field(default_factory=dict)   # 网络中心性度量
    
    # 预测置信度
    confidence_score: float = 1.0          # 预测置信度分数
    evidence_sources: List[str] = field(default_factory=list)  # 证据来源
    
    # 其他注释信息
    cellular_location: str = ''            # 细胞定位
    enzyme_class: str = ''                 # 酶分类（EC号）
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)


class BiGGModelsClient:
    """BiGG Models数据库客户端 - 连接真实的BiGG Models API"""
    
    def __init__(self, base_url: str = None, static_url: str = None):
        """
        初始化BiGG Models客户端
        
        参数:
            base_url: BiGG Models API基础URL
            static_url: BiGG Models静态文件URL
        """
        self.base_url = base_url or "http://bigg.ucsd.edu/api/v2"
        self.static_url = static_url or "http://bigg.ucsd.edu/static/models"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Clostridium-Analysis-Pipeline/3.0.0',
            'Accept': 'application/json'
        })
        
        logger.info(f"BiGG Models客户端初始化完成: {self.base_url}")
    
    def get_models_list(self) -> List[Dict[str, Any]]:
        """
        获取BiGG Models中所有可用的模型列表
        
        返回:
            模型信息列表
        """
        try:
            url = urljoin(self.base_url, "models")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            models = data.get('results', [])
            
            logger.info(f"获取到 {len(models)} 个可用模型")
            return models
            
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            return []
    
    def find_clostridium_models(self) -> List[Dict[str, Any]]:
        """
        查找梭菌相关的代谢模型
        
        返回:
            梭菌模型列表
        """
        all_models = self.get_models_list()
        
        # 搜索关键词（基于真实的梭菌物种名称）
        clostridium_keywords = [
            'clostridium', 'clostridi', 
            'ljungdahlii', 'ljungdahl',
            'acetogen', 'acetogenic',
            'thermoaceticum', 'acetobutylicum'
        ]
        
        clostridium_models = []
        
        for model in all_models:
            model_id = model.get('bigg_id', '').lower()
            organism_name = model.get('organism', '').lower()
            
            # 检查模型ID和物种名称
            if any(keyword in model_id or keyword in organism_name 
                   for keyword in clostridium_keywords):
                clostridium_models.append(model)
                logger.info(f"找到梭菌模型: {model.get('bigg_id')} - {model.get('organism')}")
        
        if not clostridium_models:
            logger.warning("未找到梭菌特异性模型，将推荐通用厌氧菌模型")
            # 查找厌氧菌模型作为替代
            anaerobic_keywords = ['anaerobic', 'ferment', 'clostrid']
            for model in all_models:
                if any(keyword in model.get('organism', '').lower() 
                       for keyword in anaerobic_keywords):
                    clostridium_models.append(model)
        
        return clostridium_models[:5]  # 返回前5个最相关的模型
    
    def get_model_info(self, model_id: str) -> Optional[Dict[str, Any]]:
        """
        获取特定模型的详细信息
        
        参数:
            model_id: 模型ID
            
        返回:
            模型详细信息
        """
        try:
            url = urljoin(self.base_url, f"models/{model_id}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"获取模型信息失败 {model_id}: {e}")
            return None
    
    def download_model(self, model_id: str, format: str = 'sbml') -> Optional[str]:
        """
        下载指定格式的模型文件
        
        参数:
            model_id: 模型ID
            format: 文件格式（sbml, json, mat）
            
        返回:
            模型文件内容（字符串）
        """
        try:
            # 构建下载URL
            if format == 'sbml':
                url = f"{self.static_url}/{model_id}.xml"
            elif format == 'json':
                url = f"{self.static_url}/{model_id}.json"
            elif format == 'mat':
                url = f"{self.static_url}/{model_id}.mat"
            else:
                raise ValueError(f"不支持的文件格式: {format}")
            
            logger.info(f"下载模型文件: {url}")
            response = self.session.get(url, timeout=60)
            response.raise_for_status()
            
            return response.text
            
        except Exception as e:
            logger.error(f"下载模型文件失败 {model_id}: {e}")
            return None
    
    def search_metabolites(self, query: str) -> List[Dict[str, Any]]:
        """
        搜索代谢物
        
        参数:
            query: 搜索关键词
            
        返回:
            代谢物信息列表
        """
        try:
            url = urljoin(self.base_url, f"search?query={query}&search_type=metabolites")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            return data.get('results', [])
            
        except Exception as e:
            logger.error(f"搜索代谢物失败: {e}")
            return []
    
    def search_reactions(self, query: str) -> List[Dict[str, Any]]:
        """
        搜索反应
        
        参数:
            query: 搜索关键词
            
        返回:
            反应信息列表
        """
        try:
            url = urljoin(self.base_url, f"search?query={query}&search_type=reactions")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            return data.get('results', [])
            
        except Exception as e:
            logger.error(f"搜索反应失败: {e}")
            return []


class MetabolicModelAnalyzer:
    """代谢模型分析器类 - 基于真实的约束代谢建模和BiGG Models"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化代谢模型分析器
        
        参数:
            config: 配置管理器实例
        """
        self.config = config
        self.model: Optional[cobra.Model] = None
        self.bigg_client = BiGGModelsClient(
            base_url=config.database.bigg_api_url,
            static_url=config.database.bigg_static_url
        )
        self.baseline_fluxes: Dict[str, float] = {}
        self.baseline_growth_rate: float = 0.0
        self.metabolic_impacts: List[MetabolicImpact] = []
        
        # 设置COBRApy求解器
        self._setup_solver()
        
        logger.info("代谢模型分析器初始化完成")
    
    def _setup_solver(self) -> None:
        """设置优化求解器 - 自动选择最佳可用求解器"""
        try:
            # 获取可用求解器列表
            available_solvers = cobra.util.solver.interface.OPTLANG_TO_SOLVERNAME
            preferred_solvers = ['gurobi', 'cplex', 'glpk_exact', 'glpk']
            
            current_solver = None
            for solver_name in preferred_solvers:
                if solver_name in available_solvers:
                    try:
                        # 测试求解器
                        cobra.Configuration().solver = solver_name
                        current_solver = solver_name
                        logger.info(f"设置求解器: {solver_name}")
                        break
                    except Exception:
                        continue
            
            if not current_solver:
                logger.warning("未找到首选求解器，使用默认求解器")
                
        except Exception as e:
            logger.warning(f"设置求解器时出现问题: {e}")
    
    def load_metabolic_model(self, model_file: Optional[str] = None, 
                           model_id: Optional[str] = None) -> bool:
        """
        加载代谢模型（从文件或BiGG Models数据库）
        
        参数:
            model_file: 模型文件路径（SBML、JSON或MAT格式）
            model_id: BiGG Models中的模型ID
            
        返回:
            是否加载成功
        """
        try:
            # 优先从本地文件加载
            if model_file and Path(model_file).exists():
                self.model = self._load_model_from_file(model_file)
                if self.model:
                    logger.info(f"从文件加载模型成功: {model_file}")
                    return self._setup_model()
            
            # 如果指定了模型ID，从BiGG Models下载
            if model_id:
                self.model = self._load_model_from_bigg(model_id)
                if self.model:
                    logger.info(f"从BiGG Models加载模型成功: {model_id}")
                    return self._setup_model()
            
            # 自动查找适合的梭菌模型
            logger.info("自动查找适合的梭菌代谢模型...")
            clostridium_models = self.bigg_client.find_clostridium_models()
            
            for model_info in clostridium_models:
                model_id = model_info.get('bigg_id')
                logger.info(f"尝试加载模型: {model_id}")
                
                self.model = self._load_model_from_bigg(model_id)
                if self.model:
                    logger.info(f"成功加载模型: {model_id} - {model_info.get('organism')}")
                    return self._setup_model()
            
            logger.error("未能加载任何适合的代谢模型")
            return False
            
        except Exception as e:
            logger.error(f"加载代谢模型时出错: {e}")
            return False
    
    def _load_model_from_file(self, model_file: str) -> Optional[cobra.Model]:
        """从本地文件加载模型"""
        try:
            file_path = Path(model_file)
            
            if file_path.suffix.lower() in ['.xml', '.sbml']:
                model = cobra.io.read_sbml_model(str(file_path))
            elif file_path.suffix.lower() == '.json':
                model = cobra.io.load_json_model(str(file_path))
            elif file_path.suffix.lower() == '.mat':
                model = cobra.io.load_matlab_model(str(file_path))
            else:
                # 尝试按SBML格式读取
                model = cobra.io.read_sbml_model(str(file_path))
            
            logger.info(f"模型统计: {len(model.reactions)}个反应, "
                       f"{len(model.metabolites)}个代谢物, {len(model.genes)}个基因")
            return model
            
        except Exception as e:
            logger.error(f"从文件加载模型失败: {e}")
            return None
    
    def _load_model_from_bigg(self, model_id: str) -> Optional[cobra.Model]:
        """从BiGG Models加载模型"""
        try:
            # 首先检查模型信息
            model_info = self.bigg_client.get_model_info(model_id)
            if not model_info:
                return None
            
            # 下载SBML格式的模型
            sbml_content = self.bigg_client.download_model(model_id, 'sbml')
            if not sbml_content:
                return None
            
            # 保存到临时文件并加载
            with tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False) as f:
                f.write(sbml_content)
                temp_path = f.name
            
            try:
                model = cobra.io.read_sbml_model(temp_path)
                logger.info(f"从BiGG Models加载模型成功: {model_id}")
                logger.info(f"模型信息: {model_info.get('organism', 'Unknown organism')}")
                logger.info(f"模型统计: {len(model.reactions)}个反应, "
                           f"{len(model.metabolites)}个代谢物, {len(model.genes)}个基因")
                return model
            finally:
                os.unlink(temp_path)
                
        except Exception as e:
            logger.error(f"从BiGG Models加载模型失败: {e}")
            return None
    
    def _setup_model(self) -> bool:
        """设置模型参数"""
        try:
            if not self.model:
                return False
            
            # 设置培养基条件
            self._apply_medium_conditions()
            
            # 计算基线通量
            self._calculate_baseline_fluxes()
            
            # 验证模型可行性
            solution = self.model.optimize()
            if solution.status != 'optimal':
                logger.warning(f"模型优化状态异常: {solution.status}")
                return False
            
            self.baseline_growth_rate = solution.objective_value
            logger.info(f"基线生长率: {self.baseline_growth_rate:.4f}")
            
            return True
            
        except Exception as e:
            logger.error(f"设置模型时出错: {e}")
            return False
    
    def _apply_medium_conditions(self) -> None:
        """应用培养基条件（基于配置中的真实培养条件）"""
        try:
            # 首先关闭所有交换反应
            for reaction in self.model.exchanges:
                reaction.lower_bound = 0
            
            # 应用培养基条件
            medium_conditions = self.config.metabolic_model.medium_conditions
            
            for exchange_id, flux_value in medium_conditions.items():
                try:
                    if exchange_id in self.model.reactions:
                        reaction = self.model.reactions.get_by_id(exchange_id)
                        if flux_value < 0:  # 摄取
                            reaction.lower_bound = flux_value
                            reaction.upper_bound = 0
                        else:  # 分泌
                            reaction.lower_bound = 0
                            reaction.upper_bound = flux_value
                        
                        logger.debug(f"设置交换反应 {exchange_id}: [{reaction.lower_bound}, {reaction.upper_bound}]")
                except Exception as e:
                    logger.warning(f"设置交换反应失败 {exchange_id}: {e}")
            
            logger.info("培养基条件设置完成")
            
        except Exception as e:
            logger.error(f"应用培养基条件时出错: {e}")
    
    def _calculate_baseline_fluxes(self) -> None:
        """计算基线通量分布"""
        try:
            # 进行通量平衡分析（FBA）
            solution = self.model.optimize()
            
            if solution.status == 'optimal':
                self.baseline_fluxes = solution.fluxes.to_dict()
                logger.info(f"基线生长率: {solution.objective_value:.4f}")
                
                # 进行通量变化分析（FVA）
                if self.config.metabolic_model.perform_fva:
                    logger.info("进行通量变化分析（FVA）...")
                    fva_result = flux_variability_analysis(
                        self.model, 
                        fraction_of_optimum=self.config.metabolic_model.fva_fraction,
                        loopless=True
                    )
                    
                    # 保存FVA结果
                    if self.config.output.save_intermediate_files:
                        output_path = self.config.get_output_path('metabolic', 'baseline_fva.csv')
                        fva_result.to_csv(output_path)
                        logger.info(f"FVA结果已保存到: {output_path}")
                
            else:
                logger.error(f"模型优化失败: {solution.status}")
                
        except Exception as e:
            logger.error(f"计算基线通量时出错: {e}")
    
    def analyze_mutation_impacts(self, mutations: List[MutationRecord]) -> List[MetabolicImpact]:
        """
        分析突变对代谢网络的影响
        
        参数:
            mutations: 突变记录列表
            
        返回:
            代谢影响列表
        """
        if not self.model:
            raise ValueError("尚未加载代谢模型")
        
        logger.info(f"开始分析 {len(mutations)} 个突变的代谢影响...")
        
        self.metabolic_impacts.clear()
        
        # 按基因分组突变
        mutations_by_gene = defaultdict(list)
        for mutation in mutations:
            mutations_by_gene[mutation.gene_id].append(mutation)
        
        # 分析每个基因的影响
        total_genes = len(mutations_by_gene)
        for i, (gene_id, gene_mutations) in enumerate(mutations_by_gene.items(), 1):
            logger.info(f"分析基因 {gene_id} ({i}/{total_genes})...")
            
            try:
                impact = self._analyze_single_gene_impact(gene_id, gene_mutations)
                if impact:
                    self.metabolic_impacts.append(impact)
            except Exception as e:
                logger.error(f"分析基因 {gene_id} 时出错: {e}")
                continue
        
        # 保存结果
        if self.config.output.save_intermediate_files:
            self._save_metabolic_impacts()
        
        logger.info(f"完成代谢影响分析，分析了 {len(self.metabolic_impacts)} 个基因")
        return self.metabolic_impacts
    
    def _analyze_single_gene_impact(self, gene_id: str, mutations: List[MutationRecord]) -> Optional[MetabolicImpact]:
        """分析单个基因的代谢影响"""
        
        # 查找模型中对应的基因
        model_gene = self._find_gene_in_model(gene_id)
        if not model_gene:
            logger.debug(f"基因 {gene_id} 在模型中未找到")
            return None
        
        # 创建代谢影响对象
        impact = MetabolicImpact(
            gene_id=gene_id,
            gene_name=mutations[0].gene_name if mutations else gene_id,
            mutation_type=self._summarize_mutation_types(mutations),
            impact_level=self._determine_impact_level(mutations)
        )
        
        # 获取受影响的反应
        impact.affected_reactions = [r.id for r in model_gene.reactions]
        impact.reaction_names = {r.id: r.name for r in model_gene.reactions}
        
        # 分析子系统分布
        subsystem_count = defaultdict(int)
        for reaction in model_gene.reactions:
            if hasattr(reaction, 'subsystem') and reaction.subsystem:
                subsystem_count[reaction.subsystem] += 1
        impact.subsystem_distribution = dict(subsystem_count)
        
        # 根据突变类型和影响等级进行分析
        if impact.impact_level == 'HIGH':
            # 高影响突变：模拟基因敲除
            growth_impact = self._simulate_gene_knockout(model_gene)
            impact.growth_rate_change = growth_impact
            impact.is_essential = growth_impact < self.config.metabolic_model.essential_gene_threshold
            impact.essentiality_score = abs(growth_impact)
            
        elif impact.impact_level == 'MODERATE':
            # 中等影响突变：模拟酶活性降低
            flux_impact = self._simulate_reduced_activity(model_gene, reduction_factor=0.5)
            impact.reaction_flux_changes = flux_impact
            
        # 分析对特定代谢产物的影响
        impact.product_yield_changes = self._analyze_product_yield_changes(model_gene)
        
        # 分析通路影响
        impact.affected_pathways = self._identify_affected_pathways(model_gene)
        
        # 计算置信度分数
        impact.confidence_score = self._calculate_confidence_score(model_gene, mutations)
        
        # 添加证据来源
        impact.evidence_sources = ['metabolic_model_analysis', 'fba_simulation']
        if impact.is_essential:
            impact.evidence_sources.append('gene_essentiality_analysis')
        
        return impact
    
    def _find_gene_in_model(self, gene_id: str) -> Optional[cobra.core.Gene]:
        """在模型中查找基因"""
        if not self.model:
            return None
        
        # 直接匹配
        if gene_id in self.model.genes:
            return self.model.genes.get_by_id(gene_id)
        
        # 去除前缀后匹配（例如：gene_前缀）
        cleaned_id = gene_id.replace('gene_', '').replace('locus_', '')
        if cleaned_id in self.model.genes:
            return self.model.genes.get_by_id(cleaned_id)
        
        # 模糊匹配
        for gene in self.model.genes:
            if (gene_id.lower() in gene.id.lower() or 
                gene.id.lower() in gene_id.lower() or
                cleaned_id.lower() in gene.id.lower()):
                return gene
        
        # 基于基因名称匹配
        for gene in self.model.genes:
            if hasattr(gene, 'name') and gene.name:
                if (gene_id.lower() in gene.name.lower() or 
                    gene.name.lower() in gene_id.lower()):
                    return gene
        
        return None
    
    def _summarize_mutation_types(self, mutations: List[MutationRecord]) -> str:
        """总结突变类型"""
        types = [mut.effect_type for mut in mutations]
        unique_types = list(set(types))
        return '; '.join(unique_types)
    
    def _determine_impact_level(self, mutations: List[MutationRecord]) -> str:
        """确定突变影响等级"""
        high_impact_types = ['stop_gained', 'frameshift_variant', 'start_lost']
        moderate_impact_types = ['missense_variant', 'inframe_insertion', 'inframe_deletion']
        
        for mutation in mutations:
            if any(effect in mutation.effect_type for effect in high_impact_types):
                return 'HIGH'
        
        for mutation in mutations:
            if any(effect in mutation.effect_type for effect in moderate_impact_types):
                return 'MODERATE'
        
        return 'LOW'
    
    def _simulate_gene_knockout(self, gene: cobra.core.Gene) -> float:
        """模拟基因敲除的影响"""
        try:
            # 使用COBRApy的single_gene_deletion功能
            knockout_result = single_gene_deletion(
                self.model, 
                [gene.id],
                method='fba'
            )
            
            if not knockout_result.empty:
                knockout_growth = knockout_result.iloc[0]['growth']
                growth_change = knockout_growth - self.baseline_growth_rate
                return growth_change / self.baseline_growth_rate if self.baseline_growth_rate > 0 else 0
            
            return 0.0
            
        except Exception as e:
            logger.warning(f"基因敲除模拟失败 {gene.id}: {e}")
            return 0.0
    
    def _simulate_reduced_activity(self, gene: cobra.core.Gene, reduction_factor: float = 0.5) -> Dict[str, float]:
        """模拟酶活性降低的影响"""
        flux_changes = {}
        
        try:
            # 保存原始边界
            original_bounds = {}
            for reaction in gene.reactions:
                original_bounds[reaction.id] = (reaction.lower_bound, reaction.upper_bound)
                
                # 降低反应通量上下界
                if reaction.lower_bound < 0:
                    reaction.lower_bound *= reduction_factor
                if reaction.upper_bound > 0:
                    reaction.upper_bound *= reduction_factor
            
            # 优化模型
            solution = self.model.optimize()
            
            if solution.status == 'optimal':
                for reaction_id in original_bounds:
                    original_flux = self.baseline_fluxes.get(reaction_id, 0)
                    new_flux = solution.fluxes.get(reaction_id, 0)
                    flux_changes[reaction_id] = new_flux - original_flux
            
            # 恢复原始边界
            for reaction in gene.reactions:
                if reaction.id in original_bounds:
                    bounds = original_bounds[reaction.id]
                    reaction.lower_bound = bounds[0]
                    reaction.upper_bound = bounds[1]
                    
        except Exception as e:
            logger.warning(f"酶活性降低模拟失败 {gene.id}: {e}")
        
        return flux_changes
    
    def _analyze_product_yield_changes(self, gene: cobra.core.Gene) -> Dict[str, float]:
        """分析对目标产物产量的影响"""
        yield_changes = {}
        
        target_metabolites = self.config.metabolic_model.target_metabolites
        
        try:
            # 保存原始目标函数
            original_objective = self.model.objective
            
            for product_name, exchange_id in target_metabolites.items():
                if exchange_id in self.model.reactions:
                    try:
                        # 设置产物为目标函数
                        self.model.objective = exchange_id
                        
                        # 基线产量
                        baseline_solution = self.model.optimize()
                        baseline_yield = baseline_solution.objective_value if baseline_solution.status == 'optimal' else 0
                        
                        # 模拟基因敲除后的产量
                        knockout_solution = single_gene_deletion(
                            self.model, 
                            [gene.id], 
                            method='fba'
                        )
                        
                        if not knockout_solution.empty:
                            knockout_yield = knockout_solution.iloc[0]['growth']
                            yield_change = knockout_yield - baseline_yield
                            yield_changes[product_name] = yield_change
                            
                    except Exception as e:
                        logger.debug(f"分析产物 {product_name} 产量变化失败: {e}")
            
            # 恢复原始目标函数
            self.model.objective = original_objective
            
        except Exception as e:
            logger.warning(f"分析产物产量变化失败: {e}")
        
        return yield_changes
    
    def _identify_affected_pathways(self, gene: cobra.core.Gene) -> List[str]:
        """识别受影响的代谢通路"""
        affected_pathways = []
        
        # 基于反应的子系统注释
        subsystems = set()
        for reaction in gene.reactions:
            if hasattr(reaction, 'subsystem') and reaction.subsystem:
                subsystems.add(reaction.subsystem)
        
        affected_pathways.extend(list(subsystems))
        
        # 基于Wood-Ljungdahl通路的特殊检查
        wlp_reactions = self.config.metabolic_model.wlp_reactions
        gene_reactions = [r.id for r in gene.reactions]
        
        if any(rxn_id in gene_reactions for rxn_id in wlp_reactions):
            affected_pathways.append('Wood-Ljungdahl pathway')
        
        # 基于电子传递反应的检查
        et_reactions = self.config.metabolic_model.electron_transfer_reactions
        if any(rxn_id in gene_reactions for rxn_id in et_reactions):
            affected_pathways.append('Electron transport chain')
        
        return list(set(affected_pathways))
    
    def _calculate_confidence_score(self, gene: cobra.core.Gene, mutations: List[MutationRecord]) -> float:
        """计算预测置信度分数"""
        score = 1.0
        
        # 基于基因在模型中的反应数量
        reaction_count = len(gene.reactions)
        if reaction_count == 0:
            score *= 0.2  # 没有关联反应，置信度很低
        elif reaction_count == 1:
            score *= 0.6  # 只关联一个反应
        else:
            score *= min(1.0, 0.7 + reaction_count * 0.05)  # 更多反应增加置信度
        
        # 基于突变质量分数
        if mutations:
            avg_quality = sum(mut.quality for mut in mutations) / len(mutations)
            quality_factor = min(1.0, avg_quality / 50.0)  # 假设50为高质量阈值
            score *= quality_factor
        
        # 基于突变影响等级
        impact_factors = {'HIGH': 1.0, 'MODERATE': 0.8, 'LOW': 0.6}
        impact_level = self._determine_impact_level(mutations)
        score *= impact_factors.get(impact_level, 0.5)
        
        # 基于模型的完整性（有子系统注释的反应比例）
        annotated_reactions = sum(1 for r in gene.reactions 
                                if hasattr(r, 'subsystem') and r.subsystem)
        if reaction_count > 0:
            annotation_ratio = annotated_reactions / reaction_count
            score *= (0.8 + 0.2 * annotation_ratio)
        
        return min(1.0, score)
    
    def _save_metabolic_impacts(self) -> None:
        """保存代谢影响分析结果"""
        if not self.metabolic_impacts:
            return
        
        # 转换为DataFrame
        impact_data = []
        for impact in self.metabolic_impacts:
            impact_dict = asdict(impact)
            # 将复杂字段转换为字符串
            impact_dict['affected_reactions'] = '; '.join(impact.affected_reactions)
            impact_dict['affected_pathways'] = '; '.join(impact.affected_pathways)
            impact_dict['evidence_sources'] = '; '.join(impact.evidence_sources)
            impact_dict['reaction_flux_changes'] = json.dumps(impact.reaction_flux_changes, indent=2)
            impact_dict['metabolite_changes'] = json.dumps(impact.metabolite_changes, indent=2)
            impact_dict['product_yield_changes'] = json.dumps(impact.product_yield_changes, indent=2)
            impact_dict['pathway_flux_changes'] = json.dumps(impact.pathway_flux_changes, indent=2)
            impact_dict['subsystem_distribution'] = json.dumps(impact.subsystem_distribution, indent=2)
            impact_dict['centrality_measures'] = json.dumps(impact.centrality_measures, indent=2)
            
            impact_data.append(impact_dict)
        
        df = pd.DataFrame(impact_data)
        
        # 保存到CSV文件
        output_path = self.config.get_output_path('metabolic', 'metabolic_impacts.csv')
        df.to_csv(output_path, index=False)
        logger.info(f"代谢影响分析结果已保存到: {output_path}")
        
        # 保存到Excel文件（包含多个sheet）
        excel_path = self.config.get_output_path('metabolic', 'metabolic_impacts.xlsx')
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # 主要结果
            df.to_excel(writer, sheet_name='Metabolic Impacts', index=False)
            
            # 必需基因汇总
            essential_genes = [impact for impact in self.metabolic_impacts if impact.is_essential]
            if essential_genes:
                essential_df = pd.DataFrame([asdict(gene) for gene in essential_genes])
                essential_df.to_excel(writer, sheet_name='Essential Genes', index=False)
            
            # 高影响基因汇总
            high_impact_genes = [impact for impact in self.metabolic_impacts if impact.impact_level == 'HIGH']
            if high_impact_genes:
                high_impact_df = pd.DataFrame([asdict(gene) for gene in high_impact_genes])
                high_impact_df.to_excel(writer, sheet_name='High Impact Genes', index=False)
        
        logger.info(f"Excel格式结果已保存到: {excel_path}")
        
        # 保存详细的JSON格式数据
        json_path = self.config.get_output_path('metabolic', 'metabolic_impacts.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump([impact.to_dict() for impact in self.metabolic_impacts], 
                     f, ensure_ascii=False, indent=2)
        logger.info(f"JSON格式结果已保存到: {json_path}")
    
    def generate_pathway_impact_report(self) -> Dict[str, Any]:
        """生成通路影响报告"""
        if not self.metabolic_impacts:
            return {}
        
        # 统计分析
        total_genes = len(self.metabolic_impacts)
        essential_genes = [impact for impact in self.metabolic_impacts if impact.is_essential]
        high_impact_genes = [impact for impact in self.metabolic_impacts if impact.impact_level == 'HIGH']
        
        # 通路统计
        pathway_counts = defaultdict(int)
        for impact in self.metabolic_impacts:
            for pathway in impact.affected_pathways:
                pathway_counts[pathway] += 1
        
        # 子系统统计
        subsystem_genes = defaultdict(list)
        for impact in self.metabolic_impacts:
            for subsystem, count in impact.subsystem_distribution.items():
                subsystem_genes[subsystem].append(impact.gene_id)
        
        # 关键基因识别
        critical_genes = []
        for impact in self.metabolic_impacts:
            if (impact.is_essential or 
                impact.impact_level == 'HIGH' or 
                len(impact.affected_pathways) >= 2 or
                any(abs(change) > 0.1 for change in impact.product_yield_changes.values())):
                critical_genes.append({
                    'gene_id': impact.gene_id,
                    'gene_name': impact.gene_name,
                    'reason': self._determine_criticality_reason(impact),
                    'confidence': impact.confidence_score
                })
        
        report = {
            'summary': {
                'total_analyzed_genes': total_genes,
                'essential_genes': len(essential_genes),
                'high_impact_genes': len(high_impact_genes),
                'critical_genes': len(critical_genes),
                'affected_pathways': len(pathway_counts)
            },
            'pathway_distribution': dict(pathway_counts),
            'subsystem_impact': {k: len(v) for k, v in subsystem_genes.items()},
            'critical_genes': critical_genes,
            'model_statistics': {
                'total_reactions': len(self.model.reactions) if self.model else 0,
                'total_metabolites': len(self.model.metabolites) if self.model else 0,
                'total_genes': len(self.model.genes) if self.model else 0,
                'baseline_growth_rate': self.baseline_growth_rate
            }
        }
        
        return report
    
    def _determine_criticality_reason(self, impact: MetabolicImpact) -> str:
        """确定基因关键性的原因"""
        reasons = []
        
        if impact.is_essential:
            reasons.append("必需基因")
        if impact.impact_level == 'HIGH':
            reasons.append("高影响突变")
        if len(impact.affected_pathways) >= 2:
            reasons.append("影响多个通路")
        if any(abs(change) > 0.1 for change in impact.product_yield_changes.values()):
            reasons.append("显著影响产物产量")
        if impact.confidence_score > 0.8:
            reasons.append("高置信度预测")
        
        return "; ".join(reasons)


# 便捷函数
def analyze_metabolic_impacts(mutations: List[MutationRecord], config: ConfigManager) -> List[MetabolicImpact]:
    """
    分析代谢影响的便捷函数
    
    参数:
        mutations: 突变记录列表
        config: 配置管理器
        
    返回:
        代谢影响列表
    """
    analyzer = MetabolicModelAnalyzer(config)
    
    if analyzer.load_metabolic_model():
        return analyzer.analyze_mutation_impacts(mutations)
    else:
        logger.error("无法加载代谢模型")
        return []


if __name__ == '__main__':
    # 测试代码
    import argparse
    from config_manager import ConfigManager
    
    parser = argparse.ArgumentParser(description='代谢模型分析器')
    parser.add_argument('--config', default='config/default_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--model', help='代谢模型文件路径')
    parser.add_argument('--model-id', help='BiGG Models中的模型ID')
    parser.add_argument('--test', action='store_true',
                       help='运行测试模式')
    parser.add_argument('--list-models', action='store_true',
                       help='列出可用的梭菌模型')
    
    args = parser.parse_args()
    
    # 加载配置
    config = ConfigManager(args.config)
    
    # 创建分析器
    analyzer = MetabolicModelAnalyzer(config)
    
    if args.list_models:
        # 列出可用模型
        print("查找可用的梭菌代谢模型...")
        models = analyzer.bigg_client.find_clostridium_models()
        if models:
            print(f"找到 {len(models)} 个相关模型:")
            for model in models:
                print(f"  - {model.get('bigg_id')}: {model.get('organism')}")
        else:
            print("未找到合适的梭菌模型")
    elif args.test:
        # 测试模式：加载模型并显示基本信息
        success = analyzer.load_metabolic_model(args.model, args.model_id)
        if success:
            print(f"\n模型加载成功！")
            print(f"反应数: {len(analyzer.model.reactions)}")
            print(f"代谢物数: {len(analyzer.model.metabolites)}")
            print(f"基因数: {len(analyzer.model.genes)}")
            print(f"基线生长率: {analyzer.baseline_growth_rate:.4f}")
            
            # 显示前10个反应
            print("\n前10个反应:")
            for i, reaction in enumerate(analyzer.model.reactions[:10]):
                print(f"  {reaction.id}: {reaction.name}")
        else:
            print("模型加载失败")
    else:
        print("请使用 --test 参数运行测试模式或 --list-models 查看可用模型")