# 梭菌突变分析工作流程配置文件
# 版本: 3.0.0
# 描述: 完整的配置示例，包含所有可用选项

# 项目基本信息
project:
  name: "clostridium_ljungdahlii_mutation_analysis"
  description: "梭菌Clostridium ljungdahlii突变对代谢网络影响分析"
  organism: "Clostridium ljungdahlii"
  strain: "DSM_13528"
  version: "3.0.0"
  created_date: "2025-01-15"

# 输入文件配置
input:
  # VCF文件路径（必需）- snpEff注释后的VCF文件
  vcf_file: "data/clostridium_mutations.snpeff.vcf"
  
  # 功能注释文件（必需）- eggNOG-mapper输出
  eggnog_annotations: "annotations/clostridium_proteins.emapper.annotations"
  
  # 参考基因组文件（可选）
  reference_genome: "reference/clostridium_ljungdahlii_DSM13528.fna"
  
  # GFF注释文件（可选）
  gff_file: "reference/clostridium_ljungdahlii_DSM13528.gff"
  
  # 代谢模型文件（可选）- SBML格式
  model_file: "models/clostridium_ljungdahlii_model.xml"
  
  # 蛋白质序列文件（可选）
  protein_sequences: "reference/clostridium_ljungdahlii_proteins.faa"

# VCF文件过滤配置
vcf_filter:
  # 非同义突变效应类型（根据snpEff分类）
  non_synonymous_effects:
    - "missense_variant"          # 错义突变
    - "stop_gained"              # 终止密码子获得
    - "stop_lost"                # 终止密码子丢失  
    - "start_lost"               # 起始密码子丢失
    - "frameshift_variant"       # 移码突变
    - "inframe_insertion"        # 框内插入
    - "inframe_deletion"         # 框内缺失
    - "splice_acceptor_variant"  # 剪切受体变异
    - "splice_donor_variant"     # 剪切供体变异
    - "stop_retained_variant"    # 终止密码子保留变异
  
  # 影响等级过滤
  impact_levels:
    - "HIGH"        # 高影响
    - "MODERATE"    # 中等影响
  
  # 质量过滤阈值
  min_quality: 30.0              # 最小质量分数
  min_read_depth: 10             # 最小读取深度
  min_allele_frequency: 0.0      # 最小等位基因频率（纯培养物可设为0）
  
  # 转录本类型过滤（可选）
  transcript_biotypes:
    - "protein_coding"

# 代谢模型分析配置
metabolic_model:
  # FBA分析参数
  fba_tolerance: 1e-9            # FBA求解容差
  essential_gene_threshold: 0.01  # 必需基因阈值（生长率<1%视为必需）
  flux_variability: true         # 是否进行通量变化分析
  perform_fva: true             # 是否执行FVA分析
  
  # 目标代谢产物的交换反应ID（基于BiGG Models命名）
  target_metabolites:
    ethanol: "EX_etoh_e"         # 乙醇
    acetate: "EX_ac_e"           # 乙酸  
    butyrate: "EX_but_e"         # 丁酸
    butanol: "EX_btoh_e"         # 丁醇
    acetone: "EX_act_e"          # 丙酮
    lactate: "EX_lac__D_e"       # D-乳酸
    formate: "EX_for_e"          # 甲酸
    hydrogen: "EX_h2_e"          # 氢气
    
  # Wood-Ljungdahl通路关键反应
  wlp_reactions:
    - "FDH"         # 甲酸脱氢酶
    - "FTR"         # 甲酰基转移酶  
    - "MTHFC"       # 亚甲基四氢叶酸环化酶
    - "MTHFD"       # 亚甲基四氢叶酸脱氢酶
    - "MTHFR5"      # 亚甲基四氢叶酸还原酶
    - "CODH_ACS"    # CO脱氢酶/乙酰辅酶A合成酶
    - "ACLS"        # 乙酰辅酶A合成酶
    - "PTAr"        # 磷酸转乙酰酶（可逆）
    - "ACKr"        # 乙酸激酶（可逆）
    
  # 电子传递和能量相关反应
  electron_transfer_reactions:
    - "RNF"         # Rnf复合体
    - "NFN"         # Nfn复合体
    - "HYD"         # 氢化酶
    - "FDH1"        # 甲酸脱氢酶1
    - "FDH2"        # 甲酸脱氢酶2
    - "ETF"         # 电子转移黄素蛋白
    - "ETFQO"       # ETF-泛醌氧化还原酶
    - "AOR"         # 醛:铁氧还蛋白氧化还原酶
    
  # 辅因子对
  cofactor_pairs:
    nad: ["nad_c", "nadh_c"]
    nadp: ["nadp_c", "nadph_c"] 
    fdred: ["fdred_c", "fdox_c"]  # 还原型/氧化型铁氧还蛋白
    atp: ["atp_c", "adp_c", "amp_c"]
    fad: ["fad_c", "fadh2_c"]
    
  # 培养基条件设置（单位：mmol/gDW/h）
  medium_conditions:
    EX_co2_e: -10.0      # CO2输入
    EX_h2_e: -20.0       # H2输入
    EX_co_e: -10.0       # CO输入（可选）
    EX_h2o_e: -1000.0    # 水
    EX_h_e: -1000.0      # 质子
    EX_nh4_e: -10.0      # 氨
    EX_pi_e: -10.0       # 磷酸盐
    EX_so4_e: -10.0      # 硫酸盐
    EX_mg2_e: -10.0      # 镁离子
    EX_fe2_e: -10.0      # 铁离子
    EX_k_e: -10.0        # 钾离子
    EX_ca2_e: -10.0      # 钙离子

# 通路分析配置
pathway_analysis:
  # 使用的数据库
  databases:
    - "kegg"       # KEGG数据库
    - "go"         # Gene Ontology
    - "cog"        # COG功能分类
    - "pfam"       # Pfam蛋白家族
    
  # 统计参数
  significance_threshold: 0.05    # 显著性阈值
  fdr_method: "fdr_bh"           # 多重检验校正方法（fdr_bh, bonferroni, fdr_by）
  min_gene_set_size: 3           # 最小基因集大小
  max_gene_set_size: 500         # 最大基因集大小
  permutation_num: 1000          # 置换检验次数
  
  # 梭菌特异性通路定义
  custom_pathways:
    Wood_Ljungdahl_Pathway:
      - "fdhA"      # 甲酸脱氢酶α亚基
      - "fdhB"      # 甲酸脱氢酶β亚基  
      - "fhs"       # 甲酰-THF合成酶
      - "fchA"      # 甲烯基-THF环化水解酶
      - "folD"      # 亚甲基-THF脱氢酶/环化水解酶
      - "metF"      # 亚甲基-THF还原酶
      - "metV"      # 亚甲基-THF还原酶（电子分叉）
      - "acsA"      # 乙酰辅酶A合成酶α亚基
      - "acsB"      # 乙酰辅酶A合成酶β亚基  
      - "cooS"      # CO脱氢酶
      - "acsC"      # 乙酰辅酶A合成酶γ亚基
      - "acsD"      # 乙酰辅酶A合成酶δ亚基
      - "acsE"      # 甲基转移酶
      
    Ethanol_Production:
      - "adhE1"     # 醛/醇脱氢酶1
      - "adhE2"     # 醛/醇脱氢酶2
      - "adh1"      # 醇脱氢酶1
      - "adh2"      # 醇脱氢酶2
      - "aor1"      # 醛:铁氧还蛋白氧化还原酶1
      - "aor2"      # 醛:铁氧还蛋白氧化还原酶2
      
    Acetate_Production:
      - "pta"       # 磷酸转乙酰酶
      - "ack"       # 乙酸激酶
      - "acsA"      # 乙酰辅酶A合成酶α亚基
      - "acsB"      # 乙酰辅酶A合成酶β亚基
      
    Butyrate_Production:
      - "thl"       # 硫解酶
      - "hbd"       # 3-羟基丁酰辅酶A脱氢酶
      - "crt"       # 巴豆酰辅酶A还原酶
      - "bcd"       # 丁酰辅酶A脱氢酶
      - "etfA"      # 电子转移黄素蛋白α亚基
      - "etfB"      # 电子转移黄素蛋白β亚基
      - "buk"       # 丁酸激酶
      - "ptb"       # 磷酸转丁酰酶
      - "bdhA"      # 丁醇脱氢酶A
      - "bdhB"      # 丁醇脱氢酶B
      
    Energy_Conservation:
      - "rnfA"      # Rnf复合体A亚基
      - "rnfB"      # Rnf复合体B亚基
      - "rnfC"      # Rnf复合体C亚基
      - "rnfD"      # Rnf复合体D亚基
      - "rnfE"      # Rnf复合体E亚基
      - "rnfG"      # Rnf复合体G亚基
      - "nfnA"      # Nfn复合体A亚基
      - "nfnB"      # Nfn复合体B亚基
      - "etfA"      # 电子转移黄素蛋白A
      - "etfB"      # 电子转移黄素蛋白B
      
    Electron_Bifurcation:
      - "nfnA"      # NADH-依赖的还原型铁氧还蛋白:NADP+氧化还原酶A亚基
      - "nfnB"      # NADH-依赖的还原型铁氧还蛋白:NADP+氧化还原酶B亚基
      - "bcd"       # 丁酰辅酶A脱氢酶
      - "etfA"      # 电子转移黄素蛋白A
      - "etfB"      # 电子转移黄素蛋白B
      - "hyd"       # 氢化酶
      
    NADPH_Regeneration:
      - "gapN"      # 非磷酸化甘油醛-3-磷酸脱氢酶
      - "maeA"      # 苹果酸酶
      - "icd"       # 异柠檬酸脱氢酶
      - "gnd"       # 6-磷酸葡萄糖酸脱氢酶
      - "zwf"       # 葡萄糖-6-磷酸脱氢酶
      - "pgl"       # 6-磷酸葡萄糖内酯酶

# 功能注释配置  
annotation:
  # 是否使用eggNOG-mapper注释
  use_eggnog: true
  
  # eggNOG-mapper字段映射
  eggnog_fields:
    go_terms: "GOs"              # GO术语字段
    kegg_ko: "KEGG_ko"           # KEGG KO字段
    cog_category: "COG_category"  # COG分类字段
    pfam_domains: "PFAMs"        # Pfam域字段
    description: "Description"    # 功能描述字段
    preferred_name: "Preferred_name"  # 首选基因名
    ec_number: "EC"              # EC编号字段
    cazy: "CAZy"                 # CAZy家族字段
    
  # 关键蛋白结构域（用于影响评估）
  critical_domains:
    catalytic:      # 催化域
      - "PF00171"   # 醛/醇脱氢酶
      - "PF00106"   # 短链脱氢酶/还原酶
      - "PF00107"   # 锌结合脱氢酶
      - "PF02775"   # 硫解酶N端域
      - "PF02776"   # 硫解酶C端域
      
    binding:        # 结合域
      - "PF00070"   # 吡咯喹啉醌酶重复
      - "PF00378"   # 乙酰辅酶A羧化酶
      - "PF01645"   # 保守假设蛋白
      - "PF00275"   # 叉头域
      
    regulatory:     # 调控域
      - "PF00027"   # cAMP结合域
      - "PF00072"   # 反应调节域
      - "PF00196"   # 细菌调控蛋白，luxR家族
      - "PF02954"   # HTH_3

# 输出配置
output:
  # 结果输出目录
  results_dir: "results_clostridium_analysis"
  
  # 报告生成选项
  generate_html_report: true     # 生成HTML报告
  generate_excel_report: true    # 生成Excel报告
  generate_pdf_report: false     # 生成PDF报告（需要额外依赖）
  save_plots: true              # 保存图表
  save_intermediate_files: true  # 保存中间文件
  
  # 图表格式和质量
  plot_formats:
    - "png"        # PNG格式
    - "svg"        # SVG格式
    - "pdf"        # PDF格式（高质量）
  plot_dpi: 300    # 图片分辨率
  
  # 报告模板选择
  report_template: "detailed"    # 可选: default, detailed, summary
  
  # 可视化配置
  visualization_config:
    color_scheme: "viridis"      # 颜色方案
    figure_size: [10, 8]         # 图片尺寸
    font_size: 12                # 字体大小
    show_statistics: true        # 显示统计信息

# 计算资源配置
computing:
  n_jobs: 4                      # 并行进程数
  memory_limit: "16G"            # 内存限制
  temp_dir: "/tmp"               # 临时目录
  chunk_size: 10000              # VCF文件分块大小
  use_cache: true                # 是否使用缓存
  cache_dir: ".cache"            # 缓存目录
  max_cache_size_mb: 1000        # 最大缓存大小（MB）

# 质量控制配置
quality_control:
  # 文件质量阈值
  min_file_size_mb: 0.1          # 最小文件大小
  max_file_size_mb: 10000        # 最大文件大小
  
  # VCF质量阈值
  min_vcf_quality: 20.0          # 最小VCF质量分数
  min_read_depth: 5              # 最小读取深度
  max_missing_rate: 0.1          # 最大缺失率
  
  # 注释质量阈值
  min_annotation_coverage: 0.5   # 最小注释覆盖率
  max_evalue: 0.001              # 最大E-value
  min_score: 50                  # 最小比对分数
  
  # 分析结果质量阈值
  min_sample_mutations: 10       # 最小突变数量
  max_essential_gene_ratio: 0.3  # 最大必需基因比例
  min_enrichment_pathways: 1     # 最小富集通路数

# 高级配置
advanced:
  # 数据库API配置
  api_config:
    kegg_api_delay: 0.5          # KEGG API请求间隔（秒）
    uniprot_batch_size: 100      # UniProt批量查询大小
    go_api_timeout: 30           # GO API超时时间
    
  # 算法参数
  algorithm_params:
    clustering_method: "ward"     # 聚类方法
    distance_metric: "euclidean" # 距离度量
    pca_components: 0.95         # PCA主成分数量
    
  # 调试选项
  debug:
    verbose_logging: false       # 详细日志
    save_debug_files: false      # 保存调试文件
    profile_performance: false   # 性能分析
    
  # 实验性功能
  experimental:
    enable_gpu_acceleration: false  # GPU加速（实验性）
    use_ml_prediction: false        # 机器学习预测（实验性）
    parallel_database_queries: true # 并行数据库查询

# 通知配置（可选）
notifications:
  email:
    enabled: false
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: "<EMAIL>"
    password: "your_app_password"
    recipients:
      - "<EMAIL>"
  
  slack:
    enabled: false
    webhook_url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
    channel: "#bioinformatics"

# 外部工具配置（如果需要）
external_tools:
  # 序列比对工具
  blast:
    enabled: false
    path: "/usr/local/bin/blastp"
    database: "/data/databases/nr"
    
  # 蛋白结构预测
  alphafold:
    enabled: false
    api_url: "https://alphafold.ebi.ac.uk/api"
    
  # 功能预测工具
  interpro:
    enabled: false
    path: "/usr/local/bin/interproscan.sh"