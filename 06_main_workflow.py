#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主工作流程 - 梭菌生物信息学工作流程
作者：生物信息学分析团队
版本：3.0.0
描述：整合所有分析模块，执行完整的梭菌突变分析工作流程
     确保所有模块协同工作，提供完整的错误处理和日志记录
"""

import logging
import sys
import time
import traceback
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import json
import pickle
from datetime import datetime
import psutil
import os

# 导入所有分析模块
from config_manager import ConfigManager
from vcf_processor import VCFProcessor, MutationRecord
from annotation_integrator import AnnotationIntegrator
from metabolic_analyzer import MetabolicModelAnalyzer, MetabolicImpact
from enrichment_analyzer import EnrichmentAnalyzer, EnrichmentResult
from quality_control import QualityController, QualityReport
from visualization import VisualizationGenerator
from utils import Timer, ProgressBar, SystemInfo

logger = logging.getLogger(__name__)


@dataclass
class WorkflowResults:
    """工作流程结果数据结构 - 完整记录所有分析结果"""
    # 基本信息
    workflow_version: str = "3.0.0"
    analysis_date: str = ""
    execution_time: float = 0.0
    
    # 系统信息
    system_info: Dict[str, Any] = None
    
    # 输入文件信息
    input_files: Dict[str, str] = None
    input_file_stats: Dict[str, Dict[str, Any]] = None
    
    # 各模块结果
    vcf_results: Dict[str, Any] = None
    annotation_results: Dict[str, Any] = None
    metabolic_results: Dict[str, Any] = None
    enrichment_results: Dict[str, Any] = None
    quality_results: Dict[str, Any] = None
    visualization_results: Dict[str, List[str]] = None
    
    # 统计摘要
    summary_statistics: Dict[str, Any] = None
    
    # 关键发现
    key_findings: Dict[str, Any] = None
    
    # 错误和警告
    errors: List[str] = None
    warnings: List[str] = None
    
    # 性能指标
    performance_metrics: Dict[str, Any] = None
    
    def __post_init__(self):
        """初始化默认值"""
        if self.system_info is None:
            self.system_info = {}
        if self.input_files is None:
            self.input_files = {}
        if self.input_file_stats is None:
            self.input_file_stats = {}
        if self.vcf_results is None:
            self.vcf_results = {}
        if self.annotation_results is None:
            self.annotation_results = {}
        if self.metabolic_results is None:
            self.metabolic_results = {}
        if self.enrichment_results is None:
            self.enrichment_results = {}
        if self.quality_results is None:
            self.quality_results = {}
        if self.visualization_results is None:
            self.visualization_results = {}
        if self.summary_statistics is None:
            self.summary_statistics = {}
        if self.key_findings is None:
            self.key_findings = {}
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []
        if self.performance_metrics is None:
            self.performance_metrics = {}
        
        if not self.analysis_date:
            self.analysis_date = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


class ClostridiumMutationWorkflow:
    """梭菌突变分析主工作流程类 - 集成所有分析模块"""
    
    def __init__(self, config_path: str):
        """
        初始化工作流程
        
        参数:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = None
        self.results = WorkflowResults()
        
        # 分析模块
        self.vcf_processor = None
        self.annotation_integrator = None
        self.metabolic_analyzer = None
        self.enrichment_analyzer = None
        self.quality_controller = None
        self.visualization_generator = None
        
        # 中间结果
        self.mutations: List[MutationRecord] = []
        self.metabolic_impacts: List[MetabolicImpact] = []
        self.enrichment_results_list: List[EnrichmentResult] = []
        self.quality_report: Optional[QualityReport] = None
        
        # 性能监控
        self.memory_usage = []
        self.step_timings = {}
        
        # 初始化
        self._initialize_workflow()
    
    def _initialize_workflow(self) -> None:
        """初始化工作流程"""
        try:
            logger.info("初始化梭菌突变分析工作流程...")
            
            # 记录系统信息
            self.results.system_info = SystemInfo.get_system_info()
            logger.info(f"系统信息: Python {self.results.system_info.get('python_version')}, "
                       f"内存: {self.results.system_info.get('total_memory_gb'):.1f}GB")
            
            # 加载配置
            logger.info("加载配置文件...")
            self.config = ConfigManager(self.config_path)
            
            # 验证配置
            config_errors = self.config.validate_config()
            if config_errors:
                for error in config_errors:
                    self.results.errors.append(f"配置错误: {error}")
                    logger.error(f"配置错误: {error}")
                raise ValueError("配置文件验证失败")
            
            # 记录输入文件信息
            self.results.input_files = self.config.input_files_dict
            self._analyze_input_files()
            
            # 检查依赖
            dependency_status = SystemInfo.check_dependencies()
            missing_deps = [dep for dep, status in dependency_status.items() if not status]
            if missing_deps:
                warning_msg = f"缺少依赖项: {', '.join(missing_deps)}"
                self.results.warnings.append(warning_msg)
                logger.warning(warning_msg)
            
            logger.info("工作流程初始化完成")
            
        except Exception as e:
            error_msg = f"工作流程初始化失败: {e}"
            logger.error(error_msg)
            self.results.errors.append(error_msg)
            raise
    
    def _analyze_input_files(self) -> None:
        """分析输入文件统计信息"""
        for file_type, file_path in self.results.input_files.items():
            if file_path and Path(file_path).exists():
                try:
                    file_stat = Path(file_path).stat()
                    self.results.input_file_stats[file_type] = {
                        'size_bytes': file_stat.st_size,
                        'size_mb': file_stat.st_size / (1024 * 1024),
                        'modified_time': datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                        'exists': True
                    }
                except Exception as e:
                    self.results.input_file_stats[file_type] = {
                        'exists': False,
                        'error': str(e)
                    }
    
    def run_complete_workflow(self) -> WorkflowResults:
        """
        运行完整的分析工作流程
        
        返回:
            工作流程结果
        """
        start_time = time.time()
        
        try:
            logger.info("=" * 80)
            logger.info("开始梭菌突变分析工作流程")
            logger.info("=" * 80)
            
            # 步骤1: VCF文件处理
            with Timer("VCF文件处理") as timer:
                logger.info("\n🧬 步骤1: 处理VCF文件...")
                success = self._run_vcf_processing()
                if not success:
                    raise RuntimeError("VCF文件处理失败")
                self.step_timings['vcf_processing'] = timer.duration
            
            # 步骤2: 功能注释整合
            with Timer("功能注释整合") as timer:
                logger.info("\n📝 步骤2: 整合功能注释...")
                success = self._run_annotation_integration()
                if not success:
                    raise RuntimeError("功能注释整合失败")
                self.step_timings['annotation_integration'] = timer.duration
            
            # 步骤3: 代谢模型分析
            with Timer("代谢模型分析") as timer:
                logger.info("\n⚡ 步骤3: 分析代谢影响...")
                success = self._run_metabolic_analysis()
                if not success:
                    self.results.warnings.append("代谢模型分析失败，但继续其他分析")
                    logger.warning("代谢模型分析失败，继续其他分析")
                self.step_timings['metabolic_analysis'] = timer.duration
            
            # 步骤4: 通路富集分析
            with Timer("通路富集分析") as timer:
                logger.info("\n📊 步骤4: 进行通路富集分析...")
                success = self._run_enrichment_analysis()
                if not success:
                    raise RuntimeError("通路富集分析失败")
                self.step_timings['enrichment_analysis'] = timer.duration
            
            # 步骤5: 质量控制
            with Timer("质量控制") as timer:
                logger.info("\n🔍 步骤5: 执行质量控制...")
                self._run_quality_control()
                self.step_timings['quality_control'] = timer.duration
            
            # 步骤6: 生成可视化
            with Timer("生成可视化") as timer:
                logger.info("\n📈 步骤6: 生成可视化图表...")
                self._generate_visualizations()
                self.step_timings['visualization'] = timer.duration
            
            # 步骤7: 生成综合结果
            with Timer("结果整合") as timer:
                logger.info("\n📋 步骤7: 整合分析结果...")
                self._generate_comprehensive_results()
                self.step_timings['result_integration'] = timer.duration
            
            # 步骤8: 保存最终结果
            with Timer("保存结果") as timer:
                logger.info("\n💾 步骤8: 保存分析结果...")
                self._save_final_results()
                self.step_timings['save_results'] = timer.duration
            
            # 计算执行时间和性能指标
            self.results.execution_time = time.time() - start_time
            self._calculate_performance_metrics()
            
            logger.info("\n" + "=" * 80)
            logger.info(f"🎉 工作流程执行完成！总耗时: {self.results.execution_time:.2f} 秒")
            logger.info(f"📁 结果保存在: {self.config.output.results_dir}")
            self._print_summary()
            logger.info("=" * 80)
            
            return self.results
            
        except Exception as e:
            self.results.execution_time = time.time() - start_time
            error_msg = f"工作流程执行失败: {e}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            self.results.errors.append(error_msg)
            
            # 即使失败也尝试保存部分结果
            try:
                self._save_partial_results()
            except Exception as save_error:
                logger.error(f"保存部分结果失败: {save_error}")
            
            return self.results
    
    def _run_vcf_processing(self) -> bool:
        """运行VCF文件处理"""
        try:
            # 初始化VCF处理器
            self.vcf_processor = VCFProcessor(self.config)
            
            # 加载基因注释（如果可用）
            if self.config.input_files.gff_file:
                logger.info("加载基因注释文件...")
                self.vcf_processor.load_gene_annotations()
            
            # 处理VCF文件
            vcf_file = self.config.input_files.vcf_file
            logger.info(f"处理VCF文件: {vcf_file}")
            
            self.mutations = self.vcf_processor.process_vcf(vcf_file)
            
            if not self.mutations:
                self.results.warnings.append("未发现非同义突变")
                logger.warning("未发现非同义突变")
                return False
            
            # 获取详细统计信息
            mutation_stats = self.vcf_processor.get_mutation_statistics()
            gene_summary = self.vcf_processor.get_gene_mutation_summary()
            
            # 记录VCF处理结果
            self.results.vcf_results = {
                'total_mutations': len(self.mutations),
                'affected_genes': len(set(mut.gene_id for mut in self.mutations if mut.gene_id)),
                'unique_chromosomes': len(set(mut.chromosome for mut in self.mutations)),
                'statistics': mutation_stats,
                'gene_summary_available': not gene_summary.empty,
                'top_mutated_genes': self._get_top_mutated_genes(),
                'impact_distribution': self._calculate_impact_distribution(),
                'variant_type_distribution': self._calculate_variant_type_distribution()
            }
            
            logger.info(f"✅ VCF处理完成: 发现 {len(self.mutations)} 个非同义突变，"
                       f"涉及 {self.results.vcf_results['affected_genes']} 个基因")
            return True
            
        except Exception as e:
            error_msg = f"VCF文件处理失败: {e}"
            logger.error(error_msg)
            self.results.errors.append(error_msg)
            return False
    
    def _run_annotation_integration(self) -> bool:
        """运行功能注释整合"""
        try:
            # 初始化注释整合器
            self.annotation_integrator = AnnotationIntegrator(self.config)
            
            # 加载eggNOG注释
            annotation_file = self.config.input_files.eggnog_annotations
            logger.info(f"加载eggNOG注释文件: {annotation_file}")
            
            self.annotation_integrator.load_eggnog_annotations(annotation_file)
            
            # 导出基因集（用于富集分析）
            if self.config.output.save_intermediate_files:
                logger.info("导出基因集文件...")
                self.annotation_integrator.export_gene_sets()
            
            # 保存注释数据
            self.annotation_integrator.save_annotations()
            
            # 记录注释整合结果
            self.results.annotation_results = {
                'total_annotated_genes': len(self.annotation_integrator.gene_annotations),
                'total_pathways': len(self.annotation_integrator.pathways),
                'database_distribution': self._calculate_database_distribution(),
                'annotation_coverage': self._calculate_annotation_coverage(),
                'kegg_pathways': len([p for p in self.annotation_integrator.pathways.values() if p.database == 'KEGG']),
                'go_terms': len([p for p in self.annotation_integrator.pathways.values() if p.database == 'GO']),
                'custom_pathways': len([p for p in self.annotation_integrator.pathways.values() if p.database == 'Custom'])
            }
            
            logger.info(f"✅ 注释整合完成: {self.results.annotation_results['total_annotated_genes']} 个基因注释，"
                       f"{self.results.annotation_results['total_pathways']} 个通路")
            return True
            
        except Exception as e:
            error_msg = f"功能注释整合失败: {e}"
            logger.error(error_msg)
            self.results.errors.append(error_msg)
            return False
    
    def _run_metabolic_analysis(self) -> bool:
        """运行代谢模型分析"""
        try:
            # 初始化代谢分析器
            self.metabolic_analyzer = MetabolicModelAnalyzer(self.config)
            
            # 加载代谢模型
            model_file = self.config.input_files.model_file
            logger.info("加载代谢模型...")
            
            success = self.metabolic_analyzer.load_metabolic_model(model_file)
            
            if not success:
                self.results.warnings.append("无法加载代谢模型")
                logger.warning("无法加载代谢模型，跳过代谢分析")
                return False
            
            # 分析突变影响
            logger.info("分析突变对代谢网络的影响...")
            self.metabolic_impacts = self.metabolic_analyzer.analyze_mutation_impacts(self.mutations)
            
            # 生成通路影响报告
            pathway_report = self.metabolic_analyzer.generate_pathway_impact_report()
            
            # 记录代谢分析结果
            essential_genes = [i for i in self.metabolic_impacts if i.is_essential]
            high_impact_genes = [i for i in self.metabolic_impacts if i.impact_level == 'HIGH']
            
            self.results.metabolic_results = {
                'analyzed_genes': len(self.metabolic_impacts),
                'essential_genes': len(essential_genes),
                'high_impact_genes': len(high_impact_genes),
                'pathway_impact_report': pathway_report,
                'critical_genes': [
                    {
                        'gene_id': impact.gene_id,
                        'gene_name': impact.gene_name,
                        'impact_level': impact.impact_level,
                        'is_essential': impact.is_essential,
                        'confidence': impact.confidence_score
                    }
                    for impact in (essential_genes + high_impact_genes)[:10]
                ],
                'model_statistics': {
                    'reactions': len(self.metabolic_analyzer.model.reactions),
                    'metabolites': len(self.metabolic_analyzer.model.metabolites),
                    'genes': len(self.metabolic_analyzer.model.genes),
                    'baseline_growth_rate': self.metabolic_analyzer.baseline_growth_rate
                } if self.metabolic_analyzer.model else {}
            }
            
            logger.info(f"✅ 代谢分析完成: 分析了 {len(self.metabolic_impacts)} 个基因的代谢影响")
            logger.info(f"   发现 {len(essential_genes)} 个必需基因，{len(high_impact_genes)} 个高影响基因")
            return True
            
        except Exception as e:
            error_msg = f"代谢模型分析失败: {e}"
            logger.error(error_msg)
            self.results.errors.append(error_msg)
            return False
    
    def _run_enrichment_analysis(self) -> bool:
        """运行通路富集分析"""
        try:
            # 初始化富集分析器
            self.enrichment_analyzer = EnrichmentAnalyzer(self.config, self.annotation_integrator)
            
            # 执行富集分析
            logger.info("执行通路富集分析...")
            self.enrichment_results_list = self.enrichment_analyzer.perform_enrichment_analysis(self.mutations)
            
            # 获取分析摘要
            enrichment_summary = self.enrichment_analyzer.get_enrichment_summary()
            
            # 获取顶部富集通路
            top_pathways = self.enrichment_analyzer.get_top_enriched_pathways(n=10)
            
            # 记录富集分析结果
            significant_results = [r for r in self.enrichment_results_list if r.is_significant]
            
            self.results.enrichment_results = {
                'total_pathways_analyzed': len(self.enrichment_results_list),
                'significant_pathways': len(significant_results),
                'enrichment_summary': enrichment_summary,
                'top_enriched_pathways': [
                    {
                        'pathway_id': r.pathway_id,
                        'pathway_name': r.pathway_name,
                        'database': r.database,
                        'p_value': r.p_value_hypergeometric,
                        'adjusted_p_value': r.adjusted_p_value,
                        'fold_enrichment': r.fold_enrichment,
                        'mutated_genes': r.mutated_genes_in_pathway,
                        'total_genes': r.total_genes_in_pathway,
                        'significance_level': r.significance_level
                    }
                    for r in top_pathways
                ],
                'database_summary': {
                    'KEGG': len([r for r in significant_results if r.database == 'KEGG']),
                    'GO': len([r for r in significant_results if r.database == 'GO']),
                    'Custom': len([r for r in significant_results if r.database == 'Custom'])
                }
            }
            
            logger.info(f"✅ 富集分析完成: 分析了 {len(self.enrichment_results_list)} 个通路，"
                       f"发现 {len(significant_results)} 个显著富集的通路")
            return True
            
        except Exception as e:
            error_msg = f"通路富集分析失败: {e}"
            logger.error(error_msg)
            self.results.errors.append(error_msg)
            return False
    
    def _run_quality_control(self) -> None:
        """运行质量控制"""
        try:
            # 初始化质量控制器
            self.quality_controller = QualityController(self.config)
            
            # 执行综合质量检查
            logger.info("执行质量控制检查...")
            self.quality_report = self.quality_controller.run_comprehensive_quality_check(
                self.mutations,
                self.metabolic_impacts,
                self.enrichment_results_list
            )
            
            # 记录质量控制结果
            self.results.quality_results = {
                'overall_quality_score': self.quality_report.overall_quality_score,
                'quality_grade': self.quality_report.quality_grade,
                'warnings_count': len(self.quality_report.warnings),
                'errors_count': len(self.quality_report.errors),
                'recommendations_count': len(self.quality_report.recommendations),
                'file_quality_scores': self.quality_report.file_quality_scores,
                'analysis_quality_scores': self.quality_report.analysis_quality_scores
            }
            
            # 将质量控制的警告和错误添加到主结果中
            self.results.warnings.extend(self.quality_report.warnings)
            self.results.errors.extend(self.quality_report.errors)
            
            logger.info(f"✅ 质量控制完成: 总体评分 {self.quality_report.overall_quality_score:.1f} "
                       f"({self.quality_report.quality_grade})")
            
        except Exception as e:
            error_msg = f"质量控制失败: {e}"
            logger.error(error_msg)
            self.results.errors.append(error_msg)
    
    def _generate_visualizations(self) -> None:
        """生成可视化图表"""
        try:
            if not self.config.output.generate_plots:
                logger.info("跳过可视化生成（配置禁用）")
                return
            
            # 初始化可视化生成器
            self.visualization_generator = VisualizationGenerator(self.config)
            
            # 准备注释数据
            annotation_data = None
            if self.annotation_integrator:
                annotation_data = {
                    'gene_annotations': self.annotation_integrator.gene_annotations,
                    'pathways': self.annotation_integrator.pathways
                }
            
            # 生成所有可视化
            logger.info("生成可视化图表...")
            visualization_files = self.visualization_generator.generate_all_visualizations(
                self.mutations,
                self.metabolic_impacts,
                self.enrichment_results_list,
                annotation_data
            )
            
            self.results.visualization_results = visualization_files
            
            total_plots = sum(len(files) for files in visualization_files.values())
            logger.info(f"✅ 可视化生成完成: 创建了 {total_plots} 个图表文件")
            
        except Exception as e:
            error_msg = f"可视化生成失败: {e}"
            logger.error(error_msg)
            self.results.errors.append(error_msg)
    
    def _generate_comprehensive_results(self) -> None:
        """生成综合分析结果"""
        try:
            logger.info("整合综合分析结果...")
            
            # 生成统计摘要
            self.results.summary_statistics = {
                'input_data': {
                    'total_mutations': len(self.mutations),
                    'affected_genes': len(set(mut.gene_id for mut in self.mutations if mut.gene_id)),
                    'chromosomes': len(set(mut.chromosome for mut in self.mutations)),
                    'annotated_genes': len(self.annotation_integrator.gene_annotations) if self.annotation_integrator else 0
                },
                'pathway_analysis': {
                    'pathways_analyzed': len(self.enrichment_results_list),
                    'significant_pathways': len([r for r in self.enrichment_results_list if r.is_significant]),
                    'enrichment_rate': len([r for r in self.enrichment_results_list if r.is_significant]) / len(self.enrichment_results_list) if self.enrichment_results_list else 0
                },
                'metabolic_analysis': {
                    'analyzed_genes': len(self.metabolic_impacts),
                    'essential_genes': len([i for i in self.metabolic_impacts if i.is_essential]),
                    'high_impact_genes': len([i for i in self.metabolic_impacts if i.impact_level == 'HIGH'])
                } if self.metabolic_impacts else {},
                'quality_metrics': {
                    'overall_score': self.quality_report.overall_quality_score if self.quality_report else 0,
                    'quality_grade': self.quality_report.quality_grade if self.quality_report else 'Unknown'
                }
            }
            
            # 生成关键发现
            self.results.key_findings = self._identify_key_findings()
            
            logger.info("✅ 综合结果整合完成")
            
        except Exception as e:
            error_msg = f"综合结果生成失败: {e}"
            logger.error(error_msg)
            self.results.errors.append(error_msg)
    
    def _identify_key_findings(self) -> Dict[str, Any]:
        """识别关键发现"""
        findings = {
            'top_mutated_genes': [],
            'essential_mutated_genes': [],
            'highly_enriched_pathways': [],
            'metabolic_bottlenecks': [],
            'quality_issues': []
        }
        
        try:
            # 顶部突变基因
            if self.mutations:
                gene_mutation_counts = {}
                for mut in self.mutations:
                    if mut.gene_id:
                        gene_mutation_counts[mut.gene_id] = gene_mutation_counts.get(mut.gene_id, 0) + 1
                
                top_genes = sorted(gene_mutation_counts.items(), key=lambda x: x[1], reverse=True)[:5]
                findings['top_mutated_genes'] = [
                    {'gene_id': gene_id, 'mutation_count': count} 
                    for gene_id, count in top_genes
                ]
            
            # 必需基因突变
            if self.metabolic_impacts:
                essential_mutated = [
                    {
                        'gene_id': impact.gene_id,
                        'gene_name': impact.gene_name,
                        'impact_level': impact.impact_level,
                        'confidence': impact.confidence_score
                    }
                    for impact in self.metabolic_impacts 
                    if impact.is_essential
                ]
                findings['essential_mutated_genes'] = essential_mutated[:5]
            
            # 高度富集的通路
            if self.enrichment_results_list:
                highly_enriched = [
                    {
                        'pathway_name': result.pathway_name,
                        'database': result.database,
                        'fold_enrichment': result.fold_enrichment,
                        'p_value': result.adjusted_p_value,
                        'mutated_genes': result.mutated_genes_in_pathway
                    }
                    for result in self.enrichment_results_list
                    if result.is_significant and result.fold_enrichment > 3.0
                ]
                findings['highly_enriched_pathways'] = highly_enriched[:5]
            
            # 质量问题
            if self.quality_report:
                findings['quality_issues'] = self.quality_report.warnings[:5]
            
        except Exception as e:
            logger.warning(f"识别关键发现时出错: {e}")
        
        return findings
    
    def _calculate_performance_metrics(self) -> None:
        """计算性能指标"""
        self.results.performance_metrics = {
            'total_execution_time': self.results.execution_time,
            'step_timings': self.step_timings,
            'memory_usage': {
                'peak_memory_mb': max(self.memory_usage) if self.memory_usage else 0,
                'current_memory_mb': psutil.Process().memory_info().rss / 1024 / 1024
            },
            'throughput': {
                'mutations_per_second': len(self.mutations) / self.results.execution_time if self.results.execution_time > 0 else 0,
                'pathways_per_second': len(self.enrichment_results_list) / self.results.execution_time if self.results.execution_time > 0 else 0
            }
        }
    
    def _save_final_results(self) -> None:
        """保存最终结果"""
        try:
            # 创建输出目录
            output_dir = Path(self.config.output.results_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存完整的工作流程结果（JSON格式）
            results_file = output_dir / 'workflow_results.json'
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.results.to_dict(), f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"完整结果已保存到: {results_file}")
            
            # 保存分析摘要
            summary_file = output_dir / 'analysis_summary.json'
            summary_data = {
                'workflow_version': self.results.workflow_version,
                'analysis_date': self.results.analysis_date,
                'execution_time': self.results.execution_time,
                'summary_statistics': self.results.summary_statistics,
                'key_findings': self.results.key_findings,
                'quality_score': self.results.quality_results.get('overall_quality_score', 0),
                'error_count': len(self.results.errors),
                'warning_count': len(self.results.warnings)
            }
            
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, ensure_ascii=False, indent=2)
            logger.info(f"分析摘要已保存到: {summary_file}")
            
            # 生成HTML报告（如果配置启用）
            if self.config.output.create_html_report:
                self._generate_html_report()
            
            # 保存原始数据对象（用于后续分析）
            if self.config.output.save_intermediate_files:
                objects_file = output_dir / 'workflow_objects.pkl'
                with open(objects_file, 'wb') as f:
                    pickle.dump({
                        'mutations': self.mutations,
                        'metabolic_impacts': self.metabolic_impacts,
                        'enrichment_results': self.enrichment_results_list,
                        'quality_report': self.quality_report
                    }, f)
                logger.info(f"原始数据对象已保存到: {objects_file}")
            
        except Exception as e:
            error_msg = f"保存最终结果失败: {e}"
            logger.error(error_msg)
            self.results.errors.append(error_msg)
    
    def _save_partial_results(self) -> None:
        """保存部分结果（在出错时调用）"""
        try:
            output_dir = Path(self.config.output.results_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            partial_results = {
                'status': 'partial',
                'execution_time': self.results.execution_time,
                'errors': self.results.errors,
                'warnings': self.results.warnings,
                'completed_steps': list(self.step_timings.keys()),
                'results': {
                    'vcf_results': self.results.vcf_results if self.results.vcf_results else None,
                    'annotation_results': self.results.annotation_results if self.results.annotation_results else None,
                    'metabolic_results': self.results.metabolic_results if self.results.metabolic_results else None,
                    'enrichment_results': self.results.enrichment_results if self.results.enrichment_results else None
                }
            }
            
            partial_file = output_dir / 'partial_results.json'
            with open(partial_file, 'w', encoding='utf-8') as f:
                json.dump(partial_results, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"部分结果已保存到: {partial_file}")
            
        except Exception as e:
            logger.error(f"保存部分结果失败: {e}")
    
    def _generate_html_report(self) -> None:
        """生成HTML报告"""
        try:
            logger.info("生成HTML报告...")
            
            html_template = """
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>梭菌突变分析报告</title>
                <style>
                    body { font-family: 'Segoe UI', Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                    .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                    h1, h2, h3 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
                    .summary-box { background: #ecf0f1; padding: 20px; border-radius: 8px; margin: 20px 0; }
                    .metric { display: inline-block; margin: 10px 20px; }
                    .metric-value { font-size: 24px; font-weight: bold; color: #e74c3c; }
                    .metric-label { font-size: 14px; color: #7f8c8d; }
                    .finding-item { background: #fff3cd; padding: 15px; margin: 10px 0; border-left: 4px solid #ffc107; border-radius: 4px; }
                    .error { background: #f8d7da; border-left-color: #dc3545; }
                    .warning { background: #d1ecf1; border-left-color: #17a2b8; }
                    .success { background: #d4edda; border-left-color: #28a745; }
                    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
                    th { background-color: #f8f9fa; font-weight: bold; }
                    .footer { text-align: center; margin-top: 40px; color: #7f8c8d; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🧬 梭菌突变分析报告</h1>
                    
                    <div class="summary-box">
                        <h2>分析摘要</h2>
                        <div class="metric">
                            <div class="metric-value">{total_mutations}</div>
                            <div class="metric-label">非同义突变</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">{affected_genes}</div>
                            <div class="metric-label">受影响基因</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">{significant_pathways}</div>
                            <div class="metric-label">显著富集通路</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">{quality_score:.1f}</div>
                            <div class="metric-label">质量评分</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">{execution_time:.1f}s</div>
                            <div class="metric-label">执行时间</div>
                        </div>
                    </div>
                    
                    <h2>关键发现</h2>
                    {key_findings_html}
                    
                    <h2>分析详情</h2>
                    {analysis_details_html}
                    
                    <h2>质量控制</h2>
                    {quality_control_html}
                    
                    <div class="footer">
                        <p>报告生成时间: {analysis_date}</p>
                        <p>梭菌生物信息学工作流程 v{workflow_version}</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # 生成关键发现HTML
            key_findings_html = self._generate_key_findings_html()
            
            # 生成分析详情HTML
            analysis_details_html = self._generate_analysis_details_html()
            
            # 生成质量控制HTML
            quality_control_html = self._generate_quality_control_html()
            
            # 填充模板
            html_content = html_template.format(
                total_mutations=len(self.mutations),
                affected_genes=len(set(mut.gene_id for mut in self.mutations if mut.gene_id)),
                significant_pathways=len([r for r in self.enrichment_results_list if r.is_significant]),
                quality_score=self.results.quality_results.get('overall_quality_score', 0),
                execution_time=self.results.execution_time,
                workflow_version=self.results.workflow_version,
                analysis_date=self.results.analysis_date,
                key_findings_html=key_findings_html,
                analysis_details_html=analysis_details_html,
                quality_control_html=quality_control_html
            )
            
            # 保存HTML报告
            html_file = Path(self.config.output.results_dir) / 'analysis_report.html'
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"HTML报告已保存到: {html_file}")
            
        except Exception as e:
            logger.error(f"生成HTML报告失败: {e}")
    
    def _generate_key_findings_html(self) -> str:
        """生成关键发现的HTML"""
        html = ""
        
        try:
            findings = self.results.key_findings
            
            # 顶部突变基因
            if findings.get('top_mutated_genes'):
                html += '<div class="finding-item">'
                html += '<h3>🎯 高频突变基因</h3>'
                for gene in findings['top_mutated_genes'][:3]:
                    html += f'<p><strong>{gene["gene_id"]}</strong>: {gene["mutation_count"]} 个突变</p>'
                html += '</div>'
            
            # 必需基因突变
            if findings.get('essential_mutated_genes'):
                html += '<div class="finding-item error">'
                html += '<h3>⚠️ 必需基因突变</h3>'
                for gene in findings['essential_mutated_genes'][:3]:
                    html += f'<p><strong>{gene["gene_name"] or gene["gene_id"]}</strong>: {gene["impact_level"]} 影响</p>'
                html += '</div>'
            
            # 高度富集通路
            if findings.get('highly_enriched_pathways'):
                html += '<div class="finding-item success">'
                html += '<h3>📈 高度富集通路</h3>'
                for pathway in findings['highly_enriched_pathways'][:3]:
                    html += f'<p><strong>{pathway["pathway_name"]}</strong>: {pathway["fold_enrichment"]:.1f}倍富集</p>'
                html += '</div>'
                
        except Exception as e:
            html += f'<div class="finding-item error">关键发现生成失败: {e}</div>'
        
        return html
    
    def _generate_analysis_details_html(self) -> str:
        """生成分析详情的HTML"""
        html = ""
        
        try:
            # VCF分析结果
            if self.results.vcf_results:
                html += f"""
                <h3>VCF文件分析</h3>
                <table>
                    <tr><th>指标</th><th>数值</th></tr>
                    <tr><td>总突变数</td><td>{self.results.vcf_results['total_mutations']}</td></tr>
                    <tr><td>受影响基因</td><td>{self.results.vcf_results['affected_genes']}</td></tr>
                    <tr><td>染色体数</td><td>{self.results.vcf_results['unique_chromosomes']}</td></tr>
                </table>
                """
            
            # 富集分析结果
            if self.results.enrichment_results:
                html += f"""
                <h3>通路富集分析</h3>
                <table>
                    <tr><th>数据库</th><th>显著通路数</th></tr>
                """
                for db, count in self.results.enrichment_results.get('database_summary', {}).items():
                    html += f"<tr><td>{db}</td><td>{count}</td></tr>"
                html += "</table>"
                
        except Exception as e:
            html += f'<p class="error">分析详情生成失败: {e}</p>'
        
        return html
    
    def _generate_quality_control_html(self) -> str:
        """生成质量控制的HTML"""
        html = ""
        
        try:
            if self.results.quality_results:
                quality = self.results.quality_results
                html += f"""
                <div class="summary-box">
                    <p><strong>总体质量评分:</strong> {quality['overall_quality_score']:.1f} ({quality['quality_grade']})</p>
                    <p><strong>警告数:</strong> {quality['warnings_count']}</p>
                    <p><strong>错误数:</strong> {quality['errors_count']}</p>
                    <p><strong>建议数:</strong> {quality['recommendations_count']}</p>
                </div>
                """
                
        except Exception as e:
            html += f'<p class="error">质量控制信息生成失败: {e}</p>'
        
        return html
    
    # 辅助方法
    def _get_top_mutated_genes(self) -> List[Dict[str, Any]]:
        """获取顶部突变基因"""
        gene_counts = {}
        for mut in self.mutations:
            if mut.gene_id:
                gene_counts[mut.gene_id] = gene_counts.get(mut.gene_id, 0) + 1
        
        sorted_genes = sorted(gene_counts.items(), key=lambda x: x[1], reverse=True)
        return [{'gene_id': gene_id, 'count': count} for gene_id, count in sorted_genes[:10]]
    
    def _calculate_impact_distribution(self) -> Dict[str, int]:
        """计算影响等级分布"""
        impact_counts = {}
        for mut in self.mutations:
            impact = mut.effect_impact
            impact_counts[impact] = impact_counts.get(impact, 0) + 1
        return impact_counts
    
    def _calculate_variant_type_distribution(self) -> Dict[str, int]:
        """计算变异类型分布"""
        type_counts = {}
        for mut in self.mutations:
            var_type = mut.get_variant_type()
            type_counts[var_type] = type_counts.get(var_type, 0) + 1
        return type_counts
    
    def _calculate_database_distribution(self) -> Dict[str, int]:
        """计算数据库分布"""
        db_counts = {}
        if self.annotation_integrator:
            for pathway in self.annotation_integrator.pathways.values():
                db = pathway.database
                db_counts[db] = db_counts.get(db, 0) + 1
        return db_counts
    
    def _calculate_annotation_coverage(self) -> float:
        """计算注释覆盖率"""
        if not self.annotation_integrator or not self.mutations:
            return 0.0
        
        annotated_genes = set(self.annotation_integrator.gene_annotations.keys())
        mutated_genes = set(mut.gene_id for mut in self.mutations if mut.gene_id)
        
        if not mutated_genes:
            return 0.0
        
        covered_genes = annotated_genes & mutated_genes
        return len(covered_genes) / len(mutated_genes)
    
    def _print_summary(self) -> None:
        """打印分析摘要"""
        try:
            print("\n" + "🎯 分析摘要".center(60, "="))
            print(f"📊 突变分析: {len(self.mutations)} 个非同义突变")
            print(f"🧬 基因影响: {len(set(mut.gene_id for mut in self.mutations if mut.gene_id))} 个基因")
            
            if self.metabolic_impacts:
                essential_count = len([i for i in self.metabolic_impacts if i.is_essential])
                print(f"⚡ 代谢影响: {essential_count} 个必需基因")
            
            if self.enrichment_results_list:
                significant_count = len([r for r in self.enrichment_results_list if r.is_significant])
                print(f"📈 通路富集: {significant_count} 个显著通路")
            
            if self.quality_report:
                print(f"🔍 质量评分: {self.quality_report.overall_quality_score:.1f} ({self.quality_report.quality_grade})")
            
            print("=" * 60)
            
        except Exception as e:
            logger.warning(f"打印摘要失败: {e}")


# 便捷函数
def run_clostridium_analysis(config_path: str) -> WorkflowResults:
    """
    运行梭菌突变分析的便捷函数
    
    参数:
        config_path: 配置文件路径
        
    返回:
        工作流程结果
    """
    workflow = ClostridiumMutationWorkflow(config_path)
    return workflow.run_complete_workflow()


if __name__ == '__main__':
    import argparse
    
    # 设置命令行参数
    parser = argparse.ArgumentParser(
        description='梭菌突变分析工作流程',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
    # 使用默认配置运行完整分析
    python main_workflow.py --config config/default_config.yaml
    
    # 运行分析并生成详细日志
    python main_workflow.py --config config/my_config.yaml --verbose
    
    # 只验证配置文件
    python main_workflow.py --config config/my_config.yaml --validate-only
    
    # 继续中断的分析
    python main_workflow.py --config config/my_config.yaml --resume
        """
    )
    
    parser.add_argument('--config', '-c', 
                       default='config/default_config.yaml',
                       help='配置文件路径 (默认: config/default_config.yaml)')
    
    parser.add_argument('--output', '-o',
                       help='输出目录 (覆盖配置文件中的设置)')
    
    parser.add_argument('--verbose', '-v', 
                       action='store_true',
                       help='输出详细日志信息')
    
    parser.add_argument('--validate-only', 
                       action='store_true',
                       help='只验证配置文件，不运行分析')
    
    parser.add_argument('--resume', 
                       action='store_true',
                       help='从上次中断的地方继续分析')
    
    parser.add_argument('--skip-visualization', 
                       action='store_true',
                       help='跳过可视化生成（加快分析速度）')
    
    parser.add_argument('--skip-metabolic', 
                       action='store_true',
                       help='跳过代谢模型分析')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler(sys.stdout)]
        )
    else:
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler(sys.stdout)]
        )
    
    try:
        # 创建工作流程实例
        workflow = ClostridiumMutationWorkflow(args.config)
        
        # 如果指定了输出目录，更新配置
        if args.output:
            workflow.config.output.results_dir = args.output
        
        # 应用命令行选项
        if args.skip_visualization:
            workflow.config.output.generate_plots = False
        
        # 只验证配置
        if args.validate_only:
            errors = workflow.config.validate_config()
            if errors:
                print("❌ 配置验证失败:")
                for error in errors:
                    print(f"  - {error}")
                sys.exit(1)
            else:
                print("✅ 配置验证通过!")
                print(f"📁 输出目录: {workflow.config.output.results_dir}")
                print(f"📋 输入文件:")
                for file_type, path in workflow.config.input_files_dict.items():
                    status = "✅" if path and Path(path).exists() else "❌"
                    print(f"  {status} {file_type}: {path}")
                sys.exit(0)
        
        # 运行完整工作流程
        print("🚀 启动梭菌突变分析工作流程...")
        results = workflow.run_complete_workflow()
        
        # 检查结果
        if results.errors:
            print(f"\n❌ 分析完成但有 {len(results.errors)} 个错误:")
            for error in results.errors[:5]:  # 只显示前5个错误
                print(f"  - {error}")
            sys.exit(1)
        elif results.warnings:
            print(f"\n⚠️ 分析完成但有 {len(results.warnings)} 个警告")
            sys.exit(0)
        else:
            print("\n🎉 分析成功完成!")
            sys.exit(0)
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断分析")
        sys.exit(130)
    except FileNotFoundError as e:
        print(f"❌ 文件未找到: {e}")
        sys.exit(2)
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)