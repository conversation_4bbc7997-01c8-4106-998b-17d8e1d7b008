#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通路富集分析模块 - 梭菌生物信息学工作流程
作者：生物信息学分析团队
版本：3.0.0
描述：对突变基因进行通路富集分析，识别显著影响的生物学通路和功能分类
     基于真实的统计方法和生物信息学数据库，完全避免虚拟数据
"""

import logging
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field, asdict
from collections import defaultdict
import json
import math
from scipy import stats
from scipy.stats import hypergeom, fisher_exact, chi2_contingency
import statsmodels.stats.multitest as multitest
from statsmodels.stats.contingency_tables import mcnemar

# 导入其他模块
from config_manager import ConfigManager
from vcf_processor import MutationRecord
from annotation_integrator import AnnotationIntegrator, PathwayInfo

logger = logging.getLogger(__name__)


@dataclass
class EnrichmentResult:
    """富集分析结果数据结构 - 基于真实的统计学方法"""
    pathway_id: str                        # 通路ID
    pathway_name: str                      # 通路名称
    database: str                          # 数据库来源（KEGG, GO, Custom等）
    category: str                          # 通路分类
    
    # 基因集合信息（真实统计）
    total_genes_in_pathway: int            # 通路中的总基因数
    mutated_genes_in_pathway: int          # 通路中的突变基因数
    mutated_genes_list: List[str]          # 突变基因列表
    
    # 背景统计信息
    total_background_genes: int            # 背景基因总数（基因组中所有注释基因）
    total_mutated_genes: int               # 总突变基因数
    
    # 富集统计（基于真实的超几何分布）
    fold_enrichment: float                 # 富集倍数
    expected_count: float                  # 期望计数
    observed_count: int                    # 观察计数（即mutated_genes_in_pathway）
    
    # 统计显著性（多种方法验证）
    p_value_hypergeometric: float          # 超几何检验p值
    p_value_fisher: float                  # Fisher精确检验p值
    p_value_chi2: float                    # 卡方检验p值
    adjusted_p_value: float                # 多重检验校正后p值
    
    # 置信区间（基于真实的统计学方法）
    confidence_interval_low: float         # 置信区间下限
    confidence_interval_high: float        # 置信区间上限
    confidence_level: float = 0.95         # 置信水平
    
    # 统计功效和效应大小
    statistical_power: float = 0.0         # 统计功效
    effect_size: float = 0.0               # 效应大小（Cohen's d或类似指标）
    
    # 富集方向和显著性
    is_significant: bool = False           # 是否显著富集
    direction: str = 'enriched'            # 富集方向（enriched/depleted）
    significance_level: str = ''           # 显著性水平（highly_significant, significant, marginally_significant）
    
    # 质量控制
    statistical_warnings: List[str] = field(default_factory=list)  # 统计警告
    data_quality_score: float = 1.0       # 数据质量评分
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    def get_significance_level(self) -> str:
        """获取显著性水平描述"""
        if self.adjusted_p_value <= 0.001:
            return 'highly_significant'
        elif self.adjusted_p_value <= 0.01:
            return 'very_significant'
        elif self.adjusted_p_value <= 0.05:
            return 'significant'
        elif self.adjusted_p_value <= 0.1:
            return 'marginally_significant'
        else:
            return 'not_significant'


class StatisticalEnrichmentAnalyzer:
    """统计富集分析引擎 - 基于严格的统计学方法"""
    
    def __init__(self, significance_threshold: float = 0.05, 
                 confidence_level: float = 0.95):
        """
        初始化统计分析引擎
        
        参数:
            significance_threshold: 显著性阈值
            confidence_level: 置信水平
        """
        self.significance_threshold = significance_threshold
        self.confidence_level = confidence_level
        
        logger.info(f"统计富集分析引擎初始化完成 (α={significance_threshold}, CI={confidence_level})")
    
    def hypergeometric_test(self, k: int, M: int, n: int, N: int) -> float:
        """
        超几何检验 - 标准的富集分析方法
        
        参数:
            k: 观察到的成功次数（通路中的突变基因数）
            M: 总体中的成功状态数（总突变基因数）
            n: 抽样数（通路中的总基因数）
            N: 总体大小（背景基因总数）
            
        返回:
            p值
        """
        try:
            # 使用scipy的超几何分布
            # P(X >= k) = 1 - P(X <= k-1)
            p_value = 1 - hypergeom.cdf(k - 1, N, M, n)
            return max(p_value, 1e-300)  # 避免数值下溢
        except Exception as e:
            logger.warning(f"超几何检验计算失败: {e}")
            return 1.0
    
    def fisher_exact_test(self, k: int, M: int, n: int, N: int) -> float:
        """
        Fisher精确检验 - 小样本富集分析
        
        参数:
            k: 通路中的突变基因数
            M: 总突变基因数  
            n: 通路中的总基因数
            N: 背景基因总数
            
        返回:
            p值
        """
        try:
            # 构建2x2列联表
            # 通路中突变基因 vs 通路中非突变基因
            # 通路外突变基因 vs 通路外非突变基因
            
            a = k                           # 通路中突变基因
            b = n - k                       # 通路中非突变基因
            c = M - k                       # 通路外突变基因
            d = N - n - M + k              # 通路外非突变基因
            
            # 确保所有值都是非负的
            if a < 0 or b < 0 or c < 0 or d < 0:
                return 1.0
            
            # Fisher精确检验（单侧，富集方向）
            _, p_value = fisher_exact([[a, b], [c, d]], alternative='greater')
            
            return p_value
            
        except Exception as e:
            logger.warning(f"Fisher精确检验计算失败: {e}")
            return 1.0
    
    def chi2_test(self, k: int, M: int, n: int, N: int) -> float:
        """
        卡方检验 - 大样本富集分析
        
        参数:
            k: 通路中的突变基因数
            M: 总突变基因数
            n: 通路中的总基因数
            N: 背景基因总数
            
        返回:
            p值
        """
        try:
            # 构建2x2列联表
            a = k                           # 通路中突变基因
            b = n - k                       # 通路中非突变基因
            c = M - k                       # 通路外突变基因
            d = N - n - M + k              # 通路外非突变基因
            
            # 确保所有值都是非负的
            if a < 0 or b < 0 or c < 0 or d < 0:
                return 1.0
            
            # 检查最小期望频数（卡方检验的前提条件）
            contingency_table = np.array([[a, b], [c, d]])
            chi2_stat, p_value, dof, expected = chi2_contingency(contingency_table)
            
            # 如果期望频数过小，卡方检验不适用
            if np.min(expected) < 5:
                return 1.0  # 返回非显著结果
            
            return p_value
            
        except Exception as e:
            logger.warning(f"卡方检验计算失败: {e}")
            return 1.0
    
    def calculate_fold_enrichment(self, k: int, M: int, n: int, N: int) -> Tuple[float, float]:
        """
        计算富集倍数和期望计数
        
        参数:
            k: 通路中的突变基因数
            M: 总突变基因数
            n: 通路中的总基因数
            N: 背景基因总数
            
        返回:
            (富集倍数, 期望计数)
        """
        try:
            # 期望计数：E[X] = n * M / N
            expected = (n * M) / N if N > 0 else 0
            
            # 富集倍数：观察计数 / 期望计数
            if expected > 0:
                fold_enrichment = k / expected
            else:
                fold_enrichment = float('inf') if k > 0 else 1.0
            
            return fold_enrichment, expected
            
        except Exception as e:
            logger.warning(f"富集倍数计算失败: {e}")
            return 1.0, 0.0
    
    def calculate_confidence_interval(self, k: int, M: int, n: int, N: int, 
                                    confidence_level: float = 0.95) -> Tuple[float, float]:
        """
        计算富集倍数的置信区间
        
        参数:
            k: 通路中的突变基因数
            M: 总突变基因数
            n: 通路中的总基因数
            N: 背景基因总数
            confidence_level: 置信水平
            
        返回:
            (置信区间下限, 置信区间上限)
        """
        try:
            if k == 0:
                return 0.0, 0.0
            
            # 使用Poisson分布近似
            alpha = 1 - confidence_level
            
            # 计算置信区间
            # 对于Poisson分布，使用卡方分布的逆函数
            lower_ci = stats.chi2.ppf(alpha/2, 2*k) / (2 * (n * M / N)) if n * M > 0 else 0
            upper_ci = stats.chi2.ppf(1-alpha/2, 2*(k+1)) / (2 * (n * M / N)) if n * M > 0 else float('inf')
            
            return lower_ci, upper_ci
            
        except Exception as e:
            logger.warning(f"置信区间计算失败: {e}")
            return 0.0, float('inf')
    
    def calculate_effect_size(self, k: int, M: int, n: int, N: int) -> float:
        """
        计算效应大小 (Cramér's V)
        
        参数:
            k: 通路中的突变基因数
            M: 总突变基因数
            n: 通路中的总基因数
            N: 背景基因总数
            
        返回:
            效应大小
        """
        try:
            # 构建2x2列联表
            a = k
            b = n - k
            c = M - k
            d = N - n - M + k
            
            if a < 0 or b < 0 or c < 0 or d < 0:
                return 0.0
            
            # 计算卡方统计量
            contingency_table = np.array([[a, b], [c, d]])
            chi2_stat, _, _, _ = chi2_contingency(contingency_table)
            
            # Cramér's V = sqrt(chi2 / (n * (min(rows, cols) - 1)))
            total = a + b + c + d
            cramers_v = np.sqrt(chi2_stat / (total * 1)) if total > 0 else 0.0
            
            return cramers_v
            
        except Exception as e:
            logger.debug(f"效应大小计算失败: {e}")
            return 0.0


class EnrichmentAnalyzer:
    """通路富集分析器类 - 基于真实的统计方法和生物学数据"""
    
    def __init__(self, config: ConfigManager, annotation_integrator: AnnotationIntegrator):
        """
        初始化富集分析器
        
        参数:
            config: 配置管理器实例
            annotation_integrator: 注释整合器实例
        """
        self.config = config
        self.annotation_integrator = annotation_integrator
        self.enrichment_results: List[EnrichmentResult] = []
        
        # 分析参数（从配置中获取真实参数）
        self.significance_threshold = config.pathway_analysis.significance_threshold
        self.fdr_method = config.pathway_analysis.fdr_method
        self.min_gene_set_size = config.pathway_analysis.min_gene_set_size
        self.max_gene_set_size = config.pathway_analysis.max_gene_set_size
        self.confidence_level = config.pathway_analysis.confidence_level
        
        # 统计分析引擎
        self.statistical_analyzer = StatisticalEnrichmentAnalyzer(
            self.significance_threshold,
            self.confidence_level
        )
        
        # 背景基因集（所有注释的基因）
        self.background_genes: Set[str] = set()
        self._build_background_gene_set()
        
        logger.info("通路富集分析器初始化完成")
        logger.info(f"背景基因数: {len(self.background_genes)}")
        logger.info(f"可用通路数: {len(self.annotation_integrator.pathways)}")
    
    def _build_background_gene_set(self) -> None:
        """构建背景基因集（所有具有注释的基因）"""
        self.background_genes.clear()
        
        # 从注释整合器获取所有注释的基因
        self.background_genes.update(self.annotation_integrator.gene_annotations.keys())
        
        # 也包括通路中的所有基因
        for pathway in self.annotation_integrator.pathways.values():
            self.background_genes.update(pathway.genes)
        
        logger.info(f"构建了包含 {len(self.background_genes)} 个基因的背景基因集")
    
    def perform_enrichment_analysis(self, mutations: List[MutationRecord]) -> List[EnrichmentResult]:
        """
        执行通路富集分析
        
        参数:
            mutations: 突变记录列表
            
        返回:
            富集分析结果列表
        """
        logger.info(f"开始通路富集分析，输入 {len(mutations)} 个突变...")
        
        # 清空之前的结果
        self.enrichment_results.clear()
        
        # 提取突变基因列表
        mutated_genes = set()
        for mutation in mutations:
            if mutation.gene_id and mutation.gene_id in self.background_genes:
                mutated_genes.add(mutation.gene_id)
        
        if not mutated_genes:
            logger.warning("没有找到有效的突变基因进行富集分析")
            return []
        
        logger.info(f"分析 {len(mutated_genes)} 个突变基因 (背景: {len(self.background_genes)} 个基因)")
        
        # 对每个通路进行富集分析
        pathways_analyzed = 0
        for pathway_id, pathway in self.annotation_integrator.pathways.items():
            try:
                # 过滤通路大小
                if not (self.min_gene_set_size <= pathway.gene_count() <= self.max_gene_set_size):
                    continue
                
                # 计算通路中的突变基因
                pathway_genes = pathway.genes & self.background_genes  # 只考虑背景基因集中的基因
                mutated_in_pathway = pathway_genes & mutated_genes
                
                if not pathway_genes:  # 通路中没有背景基因
                    continue
                
                # 执行统计检验
                result = self._analyze_single_pathway(
                    pathway_id,
                    pathway,
                    mutated_in_pathway,
                    mutated_genes,
                    pathway_genes
                )
                
                if result:
                    self.enrichment_results.append(result)
                    pathways_analyzed += 1
                
            except Exception as e:
                logger.warning(f"分析通路 {pathway_id} 时出错: {e}")
                continue
        
        logger.info(f"分析了 {pathways_analyzed} 个通路")
        
        # 多重检验校正
        if self.enrichment_results:
            self._apply_multiple_testing_correction()
        
        # 排序和后处理
        self._post_process_results()
        
        # 保存结果
        if self.config.output.save_intermediate_files:
            self._save_enrichment_results()
        
        significant_count = len([r for r in self.enrichment_results if r.is_significant])
        logger.info(f"富集分析完成！发现 {significant_count} 个显著富集的通路")
        
        return self.enrichment_results
    
    def _analyze_single_pathway(self, pathway_id: str, pathway: PathwayInfo,
                               mutated_in_pathway: Set[str], all_mutated_genes: Set[str],
                               pathway_background_genes: Set[str]) -> Optional[EnrichmentResult]:
        """分析单个通路的富集情况"""
        
        # 统计参数
        k = len(mutated_in_pathway)                    # 通路中的突变基因数
        M = len(all_mutated_genes)                     # 总突变基因数
        n = len(pathway_background_genes)              # 通路中的背景基因数
        N = len(self.background_genes)                 # 总背景基因数
        
        # 基本过滤：至少需要1个突变基因
        if k == 0:
            return None
        
        # 计算各种统计量
        try:
            # 富集倍数和期望计数
            fold_enrichment, expected_count = self.statistical_analyzer.calculate_fold_enrichment(k, M, n, N)
            
            # 多种统计检验
            p_hypergeometric = self.statistical_analyzer.hypergeometric_test(k, M, n, N)
            p_fisher = self.statistical_analyzer.fisher_exact_test(k, M, n, N)
            p_chi2 = self.statistical_analyzer.chi2_test(k, M, n, N)
            
            # 置信区间
            ci_low, ci_high = self.statistical_analyzer.calculate_confidence_interval(
                k, M, n, N, self.confidence_level
            )
            
            # 效应大小
            effect_size = self.statistical_analyzer.calculate_effect_size(k, M, n, N)
            
            # 创建结果对象
            result = EnrichmentResult(
                pathway_id=pathway_id,
                pathway_name=pathway.pathway_name,
                database=pathway.database,
                category=pathway.category,
                
                # 基因统计
                total_genes_in_pathway=n,
                mutated_genes_in_pathway=k,
                mutated_genes_list=list(mutated_in_pathway),
                total_background_genes=N,
                total_mutated_genes=M,
                
                # 富集统计
                fold_enrichment=fold_enrichment,
                expected_count=expected_count,
                observed_count=k,
                
                # 统计显著性
                p_value_hypergeometric=p_hypergeometric,
                p_value_fisher=p_fisher,
                p_value_chi2=p_chi2,
                adjusted_p_value=p_hypergeometric,  # 将在多重检验校正时更新
                
                # 置信区间
                confidence_interval_low=ci_low,
                confidence_interval_high=ci_high,
                confidence_level=self.confidence_level,
                
                # 效应大小
                effect_size=effect_size,
                
                # 富集方向
                direction='enriched' if fold_enrichment > 1 else 'depleted'
            )
            
            # 数据质量检查
            self._assess_data_quality(result, k, M, n, N)
            
            return result
            
        except Exception as e:
            logger.warning(f"分析通路 {pathway_id} 的统计量时出错: {e}")
            return None
    
    def _assess_data_quality(self, result: EnrichmentResult, k: int, M: int, n: int, N: int) -> None:
        """评估数据质量和统计检验的适用性"""
        warnings = []
        quality_score = 1.0
        
        # 检查样本大小
        if M < 10:
            warnings.append("突变基因数过少，统计功效可能不足")
            quality_score *= 0.8
        
        if n < 5:
            warnings.append("通路基因数过少，可能不适合统计检验")
            quality_score *= 0.7
        
        # 检查期望频数（卡方检验的前提）
        expected = (n * M) / N if N > 0 else 0
        if expected < 5:
            warnings.append("期望频数过小，卡方检验不适用")
            quality_score *= 0.9
        
        # 检查极端比例
        if k == n:  # 通路中所有基因都发生突变
            warnings.append("通路中所有基因都发生突变，结果需谨慎解释")
            quality_score *= 0.8
        
        if M / N > 0.5:  # 超过一半的基因发生突变
            warnings.append("突变基因比例过高，可能存在批次效应")
            quality_score *= 0.9
        
        # 更新结果
        result.statistical_warnings = warnings
        result.data_quality_score = quality_score
    
    def _apply_multiple_testing_correction(self) -> None:
        """应用多重检验校正"""
        if not self.enrichment_results:
            return
        
        logger.info(f"对 {len(self.enrichment_results)} 个通路进行多重检验校正 (方法: {self.fdr_method})")
        
        # 提取p值（使用最保守的超几何检验p值）
        p_values = [result.p_value_hypergeometric for result in self.enrichment_results]
        
        # 应用校正
        try:
            if self.fdr_method == 'fdr_bh':
                # Benjamini-Hochberg FDR
                rejected, p_adjusted, _, _ = multitest.multipletests(
                    p_values, alpha=self.significance_threshold, method='fdr_bh'
                )
            elif self.fdr_method == 'fdr_by':
                # Benjamini-Yekutieli FDR
                rejected, p_adjusted, _, _ = multitest.multipletests(
                    p_values, alpha=self.significance_threshold, method='fdr_by'
                )
            elif self.fdr_method == 'bonferroni':
                # Bonferroni校正
                rejected, p_adjusted, _, _ = multitest.multipletests(
                    p_values, alpha=self.significance_threshold, method='bonferroni'
                )
            elif self.fdr_method == 'holm':
                # Holm-Bonferroni校正
                rejected, p_adjusted, _, _ = multitest.multipletests(
                    p_values, alpha=self.significance_threshold, method='holm'
                )
            else:
                logger.warning(f"未知的多重检验校正方法: {self.fdr_method}，使用Benjamini-Hochberg")
                rejected, p_adjusted, _, _ = multitest.multipletests(
                    p_values, alpha=self.significance_threshold, method='fdr_bh'
                )
            
            # 更新结果
            for i, result in enumerate(self.enrichment_results):
                result.adjusted_p_value = p_adjusted[i]
                result.is_significant = rejected[i]
                result.significance_level = result.get_significance_level()
            
            significant_count = sum(rejected)
            logger.info(f"多重检验校正后，{significant_count} 个通路显著富集")
            
        except Exception as e:
            logger.error(f"多重检验校正失败: {e}")
            # 如果校正失败，使用原始p值
            for result in self.enrichment_results:
                result.adjusted_p_value = result.p_value_hypergeometric
                result.is_significant = result.adjusted_p_value <= self.significance_threshold
                result.significance_level = result.get_significance_level()
    
    def _post_process_results(self) -> None:
        """后处理富集分析结果"""
        # 按校正后p值排序
        self.enrichment_results.sort(key=lambda x: (x.adjusted_p_value, -x.fold_enrichment))
        
        # 标记top结果
        for i, result in enumerate(self.enrichment_results):
            if i < 10 and result.is_significant:
                result.statistical_warnings.append("Top 10显著富集通路")
    
    def _save_enrichment_results(self) -> None:
        """保存富集分析结果"""
        if not self.enrichment_results:
            logger.warning("没有富集分析结果需要保存")
            return
        
        # 转换为DataFrame
        result_data = []
        for result in self.enrichment_results:
            result_dict = asdict(result)
            # 将列表字段转换为字符串
            result_dict['mutated_genes_list'] = '; '.join(result.mutated_genes_list)
            result_dict['statistical_warnings'] = '; '.join(result.statistical_warnings)
            result_data.append(result_dict)
        
        df = pd.DataFrame(result_data)
        
        # 保存完整结果
        full_results_path = self.config.get_output_path('enrichment', 'enrichment_results.csv')
        df.to_csv(full_results_path, index=False)
        logger.info(f"完整富集分析结果已保存到: {full_results_path}")
        
        # 保存显著结果
        significant_results = df[df['is_significant'] == True].copy()
        if not significant_results.empty:
            sig_results_path = self.config.get_output_path('enrichment', 'significant_enrichments.csv')
            significant_results.to_csv(sig_results_path, index=False)
            logger.info(f"显著富集结果已保存到: {sig_results_path}")
        
        # 保存Excel格式
        excel_path = self.config.get_output_path('enrichment', 'enrichment_analysis.xlsx')
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # 完整结果
            df.to_excel(writer, sheet_name='All Results', index=False)
            
            # 显著结果
            if not significant_results.empty:
                significant_results.to_excel(writer, sheet_name='Significant', index=False)
            
            # 按数据库分组
            for database in df['database'].unique():
                db_results = df[df['database'] == database].copy()
                sheet_name = f"{database}_Results"[:31]  # Excel sheet名称限制
                db_results.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # 统计摘要
            summary_data = self._generate_summary_statistics()
            summary_df = pd.DataFrame([summary_data])
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        logger.info(f"Excel格式结果已保存到: {excel_path}")
        
        # 保存JSON格式（便于程序读取）
        json_path = self.config.get_output_path('enrichment', 'enrichment_results.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump([result.to_dict() for result in self.enrichment_results], 
                     f, ensure_ascii=False, indent=2)
        logger.info(f"JSON格式结果已保存到: {json_path}")
    
    def _generate_summary_statistics(self) -> Dict[str, Any]:
        """生成富集分析统计摘要"""
        total_pathways = len(self.enrichment_results)
        significant_pathways = len([r for r in self.enrichment_results if r.is_significant])
        
        # 按数据库统计
        db_stats = defaultdict(lambda: {'total': 0, 'significant': 0, 'max_fold_enrichment': 0})
        
        for result in self.enrichment_results:
            db_stats[result.database]['total'] += 1
            if result.is_significant:
                db_stats[result.database]['significant'] += 1
            db_stats[result.database]['max_fold_enrichment'] = max(
                db_stats[result.database]['max_fold_enrichment'],
                result.fold_enrichment
            )
        
        # 富集倍数统计
        fold_enrichments = [r.fold_enrichment for r in self.enrichment_results if r.is_significant]
        fold_stats = {}
        if fold_enrichments:
            fold_stats = {
                'mean_fold_enrichment': np.mean(fold_enrichments),
                'median_fold_enrichment': np.median(fold_enrichments),
                'max_fold_enrichment': np.max(fold_enrichments),
                'min_fold_enrichment': np.min(fold_enrichments)
            }
        
        # 效应大小统计
        effect_sizes = [r.effect_size for r in self.enrichment_results if r.is_significant and r.effect_size > 0]
        effect_stats = {}
        if effect_sizes:
            effect_stats = {
                'mean_effect_size': np.mean(effect_sizes),
                'median_effect_size': np.median(effect_sizes),
                'max_effect_size': np.max(effect_sizes)
            }
        
        summary = {
            'total_pathways_analyzed': total_pathways,
            'significant_pathways': significant_pathways,
            'significance_rate': significant_pathways / total_pathways if total_pathways > 0 else 0,
            'significance_threshold': self.significance_threshold,
            'correction_method': self.fdr_method,
            'background_genes': len(self.background_genes),
            'database_statistics': dict(db_stats),
            **fold_stats,
            **effect_stats
        }
        
        return summary
    
    def get_enrichment_summary(self) -> Dict[str, Any]:
        """获取富集分析摘要"""
        return self._generate_summary_statistics()
    
    def get_top_enriched_pathways(self, n: int = 10, min_fold_enrichment: float = 1.5) -> List[EnrichmentResult]:
        """
        获取顶部富集通路
        
        参数:
            n: 返回的通路数量
            min_fold_enrichment: 最小富集倍数
            
        返回:
            顶部富集通路列表
        """
        # 过滤显著且高富集的通路
        significant_results = [
            r for r in self.enrichment_results 
            if r.is_significant and r.fold_enrichment >= min_fold_enrichment
        ]
        
        # 按校正后p值和富集倍数排序
        significant_results.sort(key=lambda x: (x.adjusted_p_value, -x.fold_enrichment))
        
        return significant_results[:n]
    
    def get_pathway_genes(self, pathway_id: str) -> List[str]:
        """
        获取通路中的基因列表
        
        参数:
            pathway_id: 通路ID
            
        返回:
            基因ID列表
        """
        for result in self.enrichment_results:
            if result.pathway_id == pathway_id:
                return result.mutated_genes_list
        
        return []
    
    def filter_results_by_database(self, database: str) -> List[EnrichmentResult]:
        """
        按数据库过滤结果
        
        参数:
            database: 数据库名称
            
        返回:
            过滤后的结果列表
        """
        return [r for r in self.enrichment_results if r.database == database]


# 便捷函数
def perform_pathway_enrichment(mutations: List[MutationRecord], 
                             annotation_integrator: AnnotationIntegrator,
                             config: ConfigManager) -> List[EnrichmentResult]:
    """
    执行通路富集分析的便捷函数
    
    参数:
        mutations: 突变记录列表
        annotation_integrator: 注释整合器
        config: 配置管理器
        
    返回:
        富集分析结果列表
    """
    analyzer = EnrichmentAnalyzer(config, annotation_integrator)
    return analyzer.perform_enrichment_analysis(mutations)


if __name__ == '__main__':
    # 测试代码
    import argparse
    from config_manager import ConfigManager
    from annotation_integrator import AnnotationIntegrator
    
    parser = argparse.ArgumentParser(description='通路富集分析器')
    parser.add_argument('--config', default='config/default_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--test-stats', action='store_true',
                       help='测试统计方法')
    parser.add_argument('--annotation-file', help='注释文件路径')
    
    args = parser.parse_args()
    
    if args.test_stats:
        # 测试统计方法
        print("测试统计富集分析方法...")
        
        analyzer = StatisticalEnrichmentAnalyzer()
        
        # 测试超几何检验
        k, M, n, N = 5, 100, 50, 1000  # 示例参数
        p_hyper = analyzer.hypergeometric_test(k, M, n, N)
        p_fisher = analyzer.fisher_exact_test(k, M, n, N)
        p_chi2 = analyzer.chi2_test(k, M, n, N)
        
        fold_enrichment, expected = analyzer.calculate_fold_enrichment(k, M, n, N)
        ci_low, ci_high = analyzer.calculate_confidence_interval(k, M, n, N)
        effect_size = analyzer.calculate_effect_size(k, M, n, N)
        
        print(f"测试参数: k={k}, M={M}, n={n}, N={N}")
        print(f"超几何检验 p值: {p_hyper:.6f}")
        print(f"Fisher精确检验 p值: {p_fisher:.6f}")
        print(f"卡方检验 p值: {p_chi2:.6f}")
        print(f"富集倍数: {fold_enrichment:.3f} (期望: {expected:.2f})")
        print(f"95%置信区间: [{ci_low:.3f}, {ci_high:.3f}]")
        print(f"效应大小: {effect_size:.3f}")
        
    elif args.annotation_file:
        # 测试完整分析流程
        config = ConfigManager(args.config)
        
        # 加载注释
        integrator = AnnotationIntegrator(config)
        integrator.load_eggnog_annotations(args.annotation_file)
        
        # 创建分析器
        analyzer = EnrichmentAnalyzer(config, integrator)
        
        print(f"富集分析器初始化完成")
        print(f"背景基因数: {len(analyzer.background_genes)}")
        print(f"可用通路数: {len(analyzer.annotation_integrator.pathways)}")
        
        # 显示通路分布
        db_counts = defaultdict(int)
        for pathway in analyzer.annotation_integrator.pathways.values():
            db_counts[pathway.database] += 1
        
        print("\n通路数据库分布:")
        for db, count in db_counts.items():
            print(f"  {db}: {count}")
        
    else:
        print("请使用 --test-stats 测试统计方法或 --annotation-file 测试完整流程")